/**
 * FoodWay Badge Component
 * Modern badge component for status indicators, ratings, and labels
 */

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ViewStyle,
  TextStyle,
} from 'react-native';
import { DesignSystem } from '../../constants/DesignSystem';

const { Colors, Typography, Spacing, BorderRadius } = DesignSystem;

interface FoodWayBadgeProps {
  text: string;
  variant?: 'primary' | 'success' | 'warning' | 'error' | 'info' | 'rating' | 'veg' | 'nonVeg';
  size?: 'small' | 'medium' | 'large';
  icon?: React.ReactNode;
}

export const FoodWayBadge: React.FC<FoodWayBadgeProps> = ({
  text,
  variant = 'primary',
  size = 'medium',
  icon,
}) => {
  const badgeStyles: ViewStyle[] = [
    styles.base,
    styles[size],
    styles[variant],
  ];

  const textStyles: TextStyle[] = [
    styles.text,
    styles[`text${size.charAt(0).toUpperCase() + size.slice(1)}` as keyof typeof styles],
    styles[`text${variant.charAt(0).toUpperCase() + variant.slice(1)}` as keyof typeof styles],
  ];

  return (
    <View style={badgeStyles}>
      {icon && <View style={styles.iconContainer}>{icon}</View>}
      <Text style={textStyles}>{text}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  base: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: BorderRadius.full,
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
  },
  
  // Sizes
  small: {
    paddingHorizontal: Spacing.xs,
    paddingVertical: 2,
  },
  medium: {
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
  },
  large: {
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
  },
  
  // Variants
  primary: {
    backgroundColor: Colors.primary,
  },
  success: {
    backgroundColor: Colors.success,
  },
  warning: {
    backgroundColor: Colors.warning,
  },
  error: {
    backgroundColor: Colors.error,
  },
  info: {
    backgroundColor: Colors.info,
  },
  rating: {
    backgroundColor: Colors.rating,
  },
  veg: {
    backgroundColor: Colors.veg,
  },
  nonVeg: {
    backgroundColor: Colors.nonVeg,
  },
  
  // Text base
  text: {
    fontFamily: Typography.fontFamily.semibold,
    fontWeight: Typography.fontWeight.semibold,
    color: Colors.white,
    textAlign: 'center',
  },
  
  // Text sizes
  textSmall: {
    fontSize: Typography.fontSize.xs,
  },
  textMedium: {
    fontSize: Typography.fontSize.sm,
  },
  textLarge: {
    fontSize: Typography.fontSize.base,
  },
  
  // Text colors (all variants use white text for now)
  textPrimary: { color: Colors.white },
  textSuccess: { color: Colors.white },
  textWarning: { color: Colors.black },
  textError: { color: Colors.white },
  textInfo: { color: Colors.white },
  textRating: { color: Colors.white },
  textVeg: { color: Colors.white },
  textNonVeg: { color: Colors.white },
  
  iconContainer: {
    marginRight: Spacing.xs,
  },
});

export default FoodWayBadge;
