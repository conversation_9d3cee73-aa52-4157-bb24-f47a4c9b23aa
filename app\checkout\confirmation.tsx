/**
 * FoodWay Order Confirmation Screen
 * Modern confirmation screen with prominent icons and order details
 */

import React, { useEffect, useRef } from 'react';
import {
  View,
  ScrollView,
  StyleSheet,
  Animated,
  TouchableOpacity,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { 
  FoodWayText,
  FoodWayCard,
  FoodWayButton,
  FoodWayBadge,
  Colors,
  DesignSystem 
} from '../../components/ui';

const { Spacing, BorderRadius, Typography, Shadows } = DesignSystem;

// Mock order data
const orderData = {
  id: 'ORD-12345',
  restaurantName: "Mario's Pizza Palace",
  restaurantImage: 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=100&h=100&fit=crop',
  estimatedTime: '25-30 min',
  items: [
    { name: 'Margherita Pizza', quantity: 1, price: 18.99 },
    { name: 'Caesar Salad', quantity: 1, price: 12.99 },
    { name: 'Garlic Bread', quantity: 2, price: 8.99 },
  ],
  subtotal: 40.97,
  deliveryFee: 2.99,
  tax: 3.15,
  total: 47.11,
  deliveryAddress: '123 Main St, Apt 4B, New York, NY 10001',
  paymentMethod: 'Credit Card ending in 1234',
};

export default function OrderConfirmationScreen() {
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.8)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;

  useEffect(() => {
    // Success animation sequence
    Animated.sequence([
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 500,
          useNativeDriver: true,
        }),
        Animated.spring(scaleAnim, {
          toValue: 1,
          tension: 50,
          friction: 8,
          useNativeDriver: true,
        }),
      ]),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 400,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  const handleTrackOrder = () => {
    router.push(`/order/${orderData.id}`);
  };

  const handleBackToHome = () => {
    router.push('/(tabs)/');
  };

  const handleReorder = () => {
    router.push(`/restaurant/mario-pizza`);
  };

  return (
    <View style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <View style={styles.content}>
          {/* Success Header */}
          <Animated.View 
            style={[
              styles.successHeader,
              {
                opacity: fadeAnim,
                transform: [{ scale: scaleAnim }],
              },
            ]}
          >
            <LinearGradient
              colors={[Colors.success, Colors.successLight]}
              style={styles.successIconContainer}
            >
              <Ionicons name="checkmark-circle" size={60} color={Colors.white} />
            </LinearGradient>
            
            <FoodWayText variant="h2" style={styles.successTitle}>
              Order Confirmed!
            </FoodWayText>
            
            <FoodWayText variant="body" color="textSecondary" style={styles.successSubtitle}>
              Your order has been placed successfully
            </FoodWayText>
          </Animated.View>

          <Animated.View
            style={[
              styles.detailsContainer,
              {
                opacity: fadeAnim,
                transform: [{ translateY: slideAnim }],
              },
            ]}
          >
            {/* Order Info Card */}
            <FoodWayCard variant="elevated" style={styles.orderInfoCard}>
              <View style={styles.orderHeader}>
                <View style={styles.orderIconContainer}>
                  <Ionicons name="receipt" size={24} color={Colors.primary} />
                </View>
                <View style={styles.orderHeaderInfo}>
                  <FoodWayText variant="h4">Order #{orderData.id}</FoodWayText>
                  <FoodWayText variant="bodySmall" color="textSecondary">
                    From {orderData.restaurantName}
                  </FoodWayText>
                </View>
                <FoodWayBadge text="Confirmed" variant="success" />
              </View>

              <View style={styles.estimatedTime}>
                <Ionicons name="time" size={20} color={Colors.primary} />
                <FoodWayText variant="body" style={styles.estimatedTimeText}>
                  Estimated delivery: {orderData.estimatedTime}
                </FoodWayText>
              </View>
            </FoodWayCard>

            {/* Order Items */}
            <FoodWayCard variant="elevated" style={styles.orderItemsCard}>
              <FoodWayText variant="h3" style={styles.cardTitle}>
                Order Items
              </FoodWayText>
              
              {orderData.items.map((item, index) => (
                <View key={index} style={styles.orderItem}>
                  <FoodWayText variant="body" style={styles.itemName}>
                    {item.quantity}x {item.name}
                  </FoodWayText>
                  <FoodWayText variant="body" color="textSecondary">
                    ${item.price.toFixed(2)}
                  </FoodWayText>
                </View>
              ))}
            </FoodWayCard>

            {/* Delivery Details */}
            <FoodWayCard variant="elevated" style={styles.deliveryCard}>
              <FoodWayText variant="h3" style={styles.cardTitle}>
                Delivery Details
              </FoodWayText>
              
              <View style={styles.deliveryRow}>
                <View style={styles.deliveryIcon}>
                  <Ionicons name="location" size={20} color={Colors.success} />
                </View>
                <View style={styles.deliveryInfo}>
                  <FoodWayText variant="bodySmall" color="textSecondary">
                    Delivering to
                  </FoodWayText>
                  <FoodWayText variant="body">
                    {orderData.deliveryAddress}
                  </FoodWayText>
                </View>
              </View>

              <View style={styles.deliveryRow}>
                <View style={styles.deliveryIcon}>
                  <Ionicons name="card" size={20} color={Colors.warning} />
                </View>
                <View style={styles.deliveryInfo}>
                  <FoodWayText variant="bodySmall" color="textSecondary">
                    Payment method
                  </FoodWayText>
                  <FoodWayText variant="body">
                    {orderData.paymentMethod}
                  </FoodWayText>
                </View>
              </View>
            </FoodWayCard>

            {/* Order Summary */}
            <FoodWayCard variant="elevated" style={styles.summaryCard}>
              <FoodWayText variant="h3" style={styles.cardTitle}>
                Order Summary
              </FoodWayText>
              
              <View style={styles.summaryRow}>
                <FoodWayText variant="body">Subtotal</FoodWayText>
                <FoodWayText variant="body">${orderData.subtotal.toFixed(2)}</FoodWayText>
              </View>
              
              <View style={styles.summaryRow}>
                <FoodWayText variant="body">Delivery Fee</FoodWayText>
                <FoodWayText variant="body">${orderData.deliveryFee.toFixed(2)}</FoodWayText>
              </View>
              
              <View style={styles.summaryRow}>
                <FoodWayText variant="body">Tax</FoodWayText>
                <FoodWayText variant="body">${orderData.tax.toFixed(2)}</FoodWayText>
              </View>
              
              <View style={[styles.summaryRow, styles.totalRow]}>
                <FoodWayText variant="h4" color="primary">Total Paid</FoodWayText>
                <FoodWayText variant="h4" color="primary">${orderData.total.toFixed(2)}</FoodWayText>
              </View>
            </FoodWayCard>

            {/* Action Buttons */}
            <View style={styles.actionButtons}>
              <FoodWayButton
                title="Track Order"
                variant="primary"
                size="large"
                fullWidth
                leftIcon={<Ionicons name="navigate" size={20} color={Colors.white} />}
                onPress={handleTrackOrder}
                style={styles.primaryButton}
              />
              
              <View style={styles.secondaryButtons}>
                <FoodWayButton
                  title="Reorder"
                  variant="outline"
                  leftIcon={<Ionicons name="repeat" size={18} color={Colors.primary} />}
                  onPress={handleReorder}
                  style={styles.secondaryButton}
                />
                
                <FoodWayButton
                  title="Back to Home"
                  variant="ghost"
                  leftIcon={<Ionicons name="home" size={18} color={Colors.primary} />}
                  onPress={handleBackToHome}
                  style={styles.secondaryButton}
                />
              </View>
            </View>

            <View style={styles.bottomSpacing} />
          </Animated.View>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },

  scrollView: {
    flex: 1,
  },

  content: {
    paddingTop: Spacing['6xl'],
  },

  successHeader: {
    alignItems: 'center',
    marginBottom: Spacing['2xl'],
  },

  successIconContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: Spacing.lg,
    ...Shadows.lg,
  },

  successTitle: {
    marginBottom: Spacing.sm,
    textAlign: 'center',
  },

  successSubtitle: {
    textAlign: 'center',
  },

  detailsContainer: {
    paddingHorizontal: Spacing.lg,
  },

  orderInfoCard: {
    marginBottom: Spacing.md,
  },

  orderHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.md,
  },

  orderIconContainer: {
    width: 40,
    height: 40,
    borderRadius: BorderRadius.full,
    backgroundColor: Colors.primaryAlpha,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: Spacing.md,
  },

  orderHeaderInfo: {
    flex: 1,
  },

  estimatedTime: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: Spacing.md,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
  },

  estimatedTimeText: {
    marginLeft: Spacing.sm,
  },

  orderItemsCard: {
    marginBottom: Spacing.md,
  },

  cardTitle: {
    marginBottom: Spacing.md,
  },

  orderItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: Spacing.xs,
  },

  itemName: {
    flex: 1,
  },

  deliveryCard: {
    marginBottom: Spacing.md,
  },

  deliveryRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: Spacing.md,
  },

  deliveryIcon: {
    width: 32,
    height: 32,
    borderRadius: BorderRadius.full,
    backgroundColor: Colors.surface,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: Spacing.md,
  },

  deliveryInfo: {
    flex: 1,
  },

  summaryCard: {
    marginBottom: Spacing.lg,
  },

  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.sm,
  },

  totalRow: {
    borderTopWidth: 1,
    borderTopColor: Colors.border,
    paddingTop: Spacing.sm,
    marginTop: Spacing.sm,
  },

  actionButtons: {
    marginBottom: Spacing.lg,
  },

  primaryButton: {
    marginBottom: Spacing.md,
  },

  secondaryButtons: {
    flexDirection: 'row',
    gap: Spacing.md,
  },

  secondaryButton: {
    flex: 1,
  },

  bottomSpacing: {
    height: Spacing['6xl'],
  },
});
