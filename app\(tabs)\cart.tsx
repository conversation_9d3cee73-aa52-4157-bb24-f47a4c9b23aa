import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { router } from 'expo-router';
import React, { useEffect, useRef } from 'react';
import {
    Alert,
    Animated,
    FlatList,
    Image,
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import {
    DesignSystem
} from '../../components/ui';
import { useCartStore } from '../../store/cartStore';
import { CartItem } from '../../types';

const { Spacing, BorderRadius, Shadows, Typography } = DesignSystem;

export default function CartScreen() {
  const {
    items,
    restaurant,
    subtotal,
    deliveryFee,
    tax,
    tip,
    total,
    removeItem,
    updateItemQuantity,
    clearCart,
  } = useCartStore();

  // Modern animations
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(30)).current;

  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 600,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 500,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  const handleQuantityChange = (itemId: string, newQuantity: number) => {
    if (newQuantity === 0) {
      Alert.alert(
        'Remove Item',
        'Are you sure you want to remove this item from your cart?',
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Remove', style: 'destructive', onPress: () => removeItem(itemId) },
        ]
      );
    } else {
      updateItemQuantity(itemId, newQuantity);
    }
  };

  const handleCheckout = () => {
    if (items.length === 0) return;
    router.push('/checkout');
  };

  const handleClearCart = () => {
    Alert.alert(
      'Clear Cart',
      'Are you sure you want to remove all items from your cart?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Clear', style: 'destructive', onPress: clearCart },
      ]
    );
  };

  const renderCartItem = ({ item, index }: { item: CartItem; index: number }) => {
    const itemAnimatedStyle = {
      opacity: fadeAnim,
      transform: [
        {
          translateY: slideAnim.interpolate({
            inputRange: [0, 30],
            outputRange: [0, 30 + index * 10],
          }),
        },
      ],
    };

    return (
      <Animated.View style={itemAnimatedStyle}>
        <View style={modernStyles.cartItem}>
          <View style={modernStyles.itemContent}>
            <View style={modernStyles.itemImageContainer}>
              <Image source={{ uri: item.menuItem.image }} style={modernStyles.itemImage} />
            </View>
            <View style={modernStyles.itemDetails}>
              <Text style={modernStyles.itemName}>{item.menuItem.name}</Text>
              <Text style={modernStyles.itemDescription} numberOfLines={2}>
                {item.menuItem.description}
              </Text>

              {/* Customizations */}
              {item.customizations.length > 0 && (
                <View style={modernStyles.customizations}>
                  {item.customizations.map((customization, index) => (
                    <View key={index} style={modernStyles.customizationChip}>
                      <Text style={modernStyles.customizationText}>
                        {customization.optionIds.join(', ')}
                      </Text>
                    </View>
                  ))}
                </View>
              )}

              {/* Add-ons */}
              {item.addOns.length > 0 && (
                <View style={modernStyles.addOns}>
                  {item.addOns.map((addOn, index) => (
                    <View key={index} style={modernStyles.addOnChip}>
                      <Ionicons name="add-circle" size={12} color={COLORS.primary} />
                      <Text style={modernStyles.addOnText}>
                        {addOn.addOnId} (x{addOn.quantity})
                      </Text>
                    </View>
                  ))}
                </View>
              )}

              <Text style={modernStyles.itemPrice}>${item.totalPrice.toFixed(2)}</Text>
            </View>
          </View>

          {/* Modern Quantity Controls */}
          <View style={modernStyles.quantityControls}>
            <TouchableOpacity
              style={modernStyles.quantityButton}
              onPress={() => handleQuantityChange(item.id, item.quantity - 1)}
              activeOpacity={0.7}
            >
              <Ionicons name="remove" size={18} color={COLORS.primary} />
            </TouchableOpacity>

            <View style={modernStyles.quantityDisplay}>
              <Text style={modernStyles.quantity}>{item.quantity}</Text>
            </View>

            <TouchableOpacity
              style={modernStyles.quantityButton}
              onPress={() => handleQuantityChange(item.id, item.quantity + 1)}
              activeOpacity={0.7}
            >
              <Ionicons name="add" size={18} color={COLORS.primary} />
            </TouchableOpacity>
          </View>
        </View>
      </Animated.View>
    );
  };

  const renderEmptyCart = () => (
    <Animated.View style={[modernStyles.emptyCart, { opacity: fadeAnim }]}>
      <View style={modernStyles.emptyCartIcon}>
        <Ionicons name="restaurant-outline" size={48} color={COLORS.primary} />
      </View>
      <Text style={modernStyles.emptyCartTitle}>Your cart is empty</Text>
      <Text style={modernStyles.emptyCartSubtitle}>
        Discover amazing restaurants and add delicious items to your cart
      </Text>
      <TouchableOpacity
        style={modernStyles.browseButton}
        onPress={() => router.push('/(tabs)')}
        activeOpacity={0.8}
      >
        <LinearGradient
          colors={['#FF6B35', '#E55A2B']}
          style={modernStyles.browseButtonGradient}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
        >
          <Ionicons name="search" size={20} color={COLORS.white} />
          <Text style={modernStyles.browseButtonText}>Browse Restaurants</Text>
        </LinearGradient>
      </TouchableOpacity>
    </Animated.View>
  );

  if (items.length === 0) {
    return (
      <SafeAreaView style={modernStyles.container}>
        {renderEmptyCart()}
      </SafeAreaView>
    );
  }

  return (
    <View style={styles.container}>
      {/* Sticky Header */}
      <FoodWayStickyHeader
        title="Your Cart"
        subtitle={restaurant ? `From ${restaurant.name}` : undefined}
        onBackPress={() => router.back()}
        onSharePress={() => console.log('Share cart')}
      />

      <ScrollView style={styles.scrollView}>
        <Animated.View style={[styles.content, { opacity: fadeAnim }]}>
          {/* Cart Header Info */}
          <FoodWayCard variant="elevated" style={styles.cartHeader}>
            <View style={styles.cartHeaderContent}>
              <View style={styles.cartIconContainer}>
                <Ionicons name="bag-check" size={24} color={Colors.primary} />
              </View>
              <View style={styles.cartHeaderInfo}>
                <FoodWayText variant="h3">Your Order</FoodWayText>
                {restaurant && (
                  <FoodWayText variant="body" color="textSecondary">
                    From {restaurant.name}
                  </FoodWayText>
                )}
              </View>
              <TouchableOpacity
                onPress={handleClearCart}
                style={styles.clearButton}
                activeOpacity={0.7}
              >
                <Ionicons name="trash" size={18} color={Colors.error} />
                <FoodWayText variant="bodySmall" color="error" style={styles.clearText}>
                  Clear
                </FoodWayText>
              </TouchableOpacity>
            </View>
          </FoodWayCard>

      {/* Modern Cart Items */}
      <FlatList
        data={items}
        renderItem={renderCartItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={modernStyles.cartList}
        showsVerticalScrollIndicator={false}
        scrollEventThrottle={16}
      />

      {/* Modern Order Summary */}
      <Animated.View style={[modernStyles.orderSummary, { opacity: fadeAnim }]}>
        <View style={modernStyles.summaryHeader}>
          <Ionicons name="receipt-outline" size={20} color={COLORS.primary} />
          <Text style={modernStyles.summaryTitle}>Order Summary</Text>
        </View>

        <View style={modernStyles.summaryContent}>
          <View style={modernStyles.summaryRow}>
            <Text style={modernStyles.summaryLabel}>Subtotal</Text>
            <Text style={modernStyles.summaryValue}>${subtotal.toFixed(2)}</Text>
          </View>

          <View style={modernStyles.summaryRow}>
            <View style={modernStyles.summaryLabelContainer}>
              <Ionicons name="bicycle-outline" size={14} color={COLORS.textSecondary} />
              <Text style={modernStyles.summaryLabel}>Delivery Fee</Text>
            </View>
            <Text style={modernStyles.summaryValue}>${deliveryFee.toFixed(2)}</Text>
          </View>

          <View style={modernStyles.summaryRow}>
            <Text style={modernStyles.summaryLabel}>Tax</Text>
            <Text style={modernStyles.summaryValue}>${tax.toFixed(2)}</Text>
          </View>

          <View style={modernStyles.summaryRow}>
            <View style={modernStyles.summaryLabelContainer}>
              <Ionicons name="heart-outline" size={14} color={COLORS.textSecondary} />
              <Text style={modernStyles.summaryLabel}>Tip</Text>
            </View>
            <Text style={modernStyles.summaryValue}>${tip.toFixed(2)}</Text>
          </View>

          <View style={modernStyles.totalRow}>
            <Text style={modernStyles.totalLabel}>Total</Text>
            <Text style={modernStyles.totalValue}>${total.toFixed(2)}</Text>
          </View>
        </View>

        <TouchableOpacity
          onPress={handleCheckout}
          style={modernStyles.checkoutButton}
          activeOpacity={0.9}
        >
          <LinearGradient
            colors={['#FF6B35', '#E55A2B']}
            style={modernStyles.checkoutButtonGradient}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
          >
            <Ionicons name="card-outline" size={20} color={COLORS.white} />
            <Text style={modernStyles.checkoutButtonText}>Proceed to Checkout</Text>
            <Ionicons name="arrow-forward" size={18} color={COLORS.white} />
          </LinearGradient>
        </TouchableOpacity>
      </Animated.View>
        </Animated.View>
      </ScrollView>
    </View>
  );
}

export default CartScreen;

// Modern styles for cart screen
const modernStyles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  headerGradient: {
    paddingTop: 10,
    paddingBottom: 15,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  cartIcon: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: SPACING.sm,
  },
  headerTitle: {
    fontSize: TYPOGRAPHY.fontSize.lg,
    fontWeight: '700',
    color: COLORS.white,
  },
  restaurantName: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    color: 'rgba(255, 255, 255, 0.8)',
    fontWeight: '500',
  },
  clearButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: SPACING.sm,
    paddingVertical: SPACING.xs,
    borderRadius: BORDER_RADIUS.md,
  },
  clearButtonText: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    color: COLORS.white,
    fontWeight: '600',
    marginLeft: 4,
  },
  cartList: {
    padding: SPACING.md,
    paddingBottom: SPACING.xl,
  },
  cartItem: {
    backgroundColor: COLORS.white,
    borderRadius: BORDER_RADIUS.lg,
    padding: SPACING.md,
    marginBottom: SPACING.md,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 3,
  },
  itemContent: {
    flexDirection: 'row',
    marginBottom: SPACING.sm,
  },
  itemImageContainer: {
    width: 80,
    height: 80,
    borderRadius: BORDER_RADIUS.md,
    overflow: 'hidden',
    marginRight: SPACING.md,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  itemImage: {
    width: '100%',
    height: '100%',
  },
  itemDetails: {
    flex: 1,
  },
  itemName: {
    fontSize: TYPOGRAPHY.fontSize.md,
    fontWeight: '700',
    color: COLORS.textPrimary,
    marginBottom: SPACING.xs,
  },
  itemDescription: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    color: COLORS.textSecondary,
    marginBottom: SPACING.xs,
    lineHeight: 18,
  },
  customizations: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: SPACING.xs,
  },
  customizationChip: {
    backgroundColor: '#F0F0F0',
    paddingHorizontal: SPACING.xs,
    paddingVertical: 2,
    borderRadius: BORDER_RADIUS.sm,
    marginRight: SPACING.xs,
    marginBottom: 4,
  },
  customizationText: {
    fontSize: TYPOGRAPHY.fontSize.xs,
    color: COLORS.textSecondary,
    fontWeight: '500',
  },
  addOns: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: SPACING.xs,
  },
  addOnChip: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 107, 53, 0.1)',
    paddingHorizontal: SPACING.xs,
    paddingVertical: 2,
    borderRadius: BORDER_RADIUS.sm,
    marginRight: SPACING.xs,
    marginBottom: 4,
  },
  addOnText: {
    fontSize: TYPOGRAPHY.fontSize.xs,
    color: COLORS.primary,
    fontWeight: '600',
    marginLeft: 2,
  },
  itemPrice: {
    fontSize: TYPOGRAPHY.fontSize.md,
    fontWeight: '700',
    color: COLORS.primary,
  },
  quantityControls: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#F8F9FA',
    borderRadius: BORDER_RADIUS.lg,
    padding: SPACING.xs,
  },
  quantityButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: COLORS.white,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  quantityDisplay: {
    backgroundColor: COLORS.primary,
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.xs,
    borderRadius: BORDER_RADIUS.md,
    marginHorizontal: SPACING.sm,
  },
  quantity: {
    fontSize: TYPOGRAPHY.fontSize.md,
    fontWeight: '700',
    color: COLORS.white,
    minWidth: 20,
    textAlign: 'center',
  },
  orderSummary: {
    backgroundColor: COLORS.white,
    margin: SPACING.md,
    marginTop: 0,
    borderRadius: BORDER_RADIUS.lg,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.12,
    shadowRadius: 12,
    elevation: 5,
  },
  summaryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: SPACING.md,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  summaryTitle: {
    fontSize: TYPOGRAPHY.fontSize.lg,
    fontWeight: '700',
    color: COLORS.textPrimary,
    marginLeft: SPACING.xs,
  },
  summaryContent: {
    padding: SPACING.md,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  summaryLabelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  summaryLabel: {
    fontSize: TYPOGRAPHY.fontSize.md,
    color: COLORS.textSecondary,
    fontWeight: '500',
    marginLeft: 4,
  },
  summaryValue: {
    fontSize: TYPOGRAPHY.fontSize.md,
    color: COLORS.textPrimary,
    fontWeight: '600',
  },
  totalRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderTopWidth: 1,
    borderTopColor: '#F0F0F0',
    paddingTop: SPACING.sm,
    marginTop: SPACING.sm,
  },
  totalLabel: {
    fontSize: TYPOGRAPHY.fontSize.lg,
    fontWeight: '700',
    color: COLORS.textPrimary,
  },
  totalValue: {
    fontSize: TYPOGRAPHY.fontSize.lg,
    fontWeight: '700',
    color: COLORS.primary,
  },
  checkoutButton: {
    margin: SPACING.md,
    borderRadius: BORDER_RADIUS.lg,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 6,
  },
  checkoutButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: SPACING.md,
    paddingHorizontal: SPACING.lg,
  },
  checkoutButtonText: {
    fontSize: TYPOGRAPHY.fontSize.md,
    fontWeight: '700',
    color: COLORS.white,
    marginHorizontal: SPACING.sm,
  },
  emptyCart: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: SPACING.xl,
  },
  emptyCartIcon: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(255, 107, 53, 0.1)',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: SPACING.lg,
  },
  emptyCartTitle: {
    fontSize: TYPOGRAPHY.fontSize.xl,
    fontWeight: '700',
    color: COLORS.textPrimary,
    marginBottom: SPACING.sm,
    textAlign: 'center',
  },
  emptyCartSubtitle: {
    fontSize: TYPOGRAPHY.fontSize.md,
    color: COLORS.textSecondary,
    textAlign: 'center',
    marginBottom: SPACING.xl,
    lineHeight: 22,
  },
  browseButton: {
    borderRadius: BORDER_RADIUS.lg,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 6,
  },
  browseButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: SPACING.md,
    paddingHorizontal: SPACING.xl,
  },
  browseButtonText: {
    fontSize: TYPOGRAPHY.fontSize.md,
    fontWeight: '700',
    color: COLORS.white,
    marginLeft: SPACING.sm,
  },
});

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    backgroundColor: COLORS.white,
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.md,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: TYPOGRAPHY.fontSize.xl,
    fontWeight: 'bold',
    color: COLORS.textPrimary,
  },
  restaurantName: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    color: COLORS.textSecondary,
    flex: 1,
    marginLeft: SPACING.sm,
  },
  clearButton: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    color: COLORS.error,
    fontWeight: '600',
  },
  cartList: {
    padding: SPACING.md,
  },
  cartItem: {
    marginBottom: SPACING.md,
  },
  itemContent: {
    flexDirection: 'row',
    marginBottom: SPACING.md,
  },
  itemImage: {
    width: 80,
    height: 80,
    borderRadius: BORDER_RADIUS.md,
    marginRight: SPACING.md,
  },
  itemDetails: {
    flex: 1,
  },
  itemName: {
    fontSize: TYPOGRAPHY.fontSize.md,
    fontWeight: 'bold',
    color: COLORS.textPrimary,
    marginBottom: SPACING.xs,
  },
  itemDescription: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    color: COLORS.textSecondary,
    marginBottom: SPACING.xs,
  },
  customizations: {
    marginBottom: SPACING.xs,
  },
  customizationText: {
    fontSize: TYPOGRAPHY.fontSize.xs,
    color: COLORS.textSecondary,
  },
  addOns: {
    marginBottom: SPACING.xs,
  },
  addOnText: {
    fontSize: TYPOGRAPHY.fontSize.xs,
    color: COLORS.primary,
  },
  itemPrice: {
    fontSize: TYPOGRAPHY.fontSize.md,
    fontWeight: 'bold',
    color: COLORS.primary,
  },
  quantityControls: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  quantityButton: {
    width: 36,
    height: 36,
    borderRadius: BORDER_RADIUS.full,
    backgroundColor: COLORS.gray[100],
    alignItems: 'center',
    justifyContent: 'center',
  },
  quantity: {
    fontSize: TYPOGRAPHY.fontSize.md,
    fontWeight: 'bold',
    color: COLORS.textPrimary,
    marginHorizontal: SPACING.md,
    minWidth: 30,
    textAlign: 'center',
  },
  orderSummary: {
    margin: SPACING.md,
    marginTop: 0,
  },
  summaryTitle: {
    fontSize: TYPOGRAPHY.fontSize.lg,
    fontWeight: 'bold',
    color: COLORS.textPrimary,
    marginBottom: SPACING.md,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  summaryLabel: {
    fontSize: TYPOGRAPHY.fontSize.md,
    color: COLORS.textSecondary,
  },
  summaryValue: {
    fontSize: TYPOGRAPHY.fontSize.md,
    color: COLORS.textPrimary,
  },
  totalRow: {
    borderTopWidth: 1,
    borderTopColor: COLORS.border,
    paddingTop: SPACING.sm,
    marginTop: SPACING.sm,
    marginBottom: SPACING.lg,
  },
  totalLabel: {
    fontSize: TYPOGRAPHY.fontSize.lg,
    fontWeight: 'bold',
    color: COLORS.textPrimary,
  },
  totalValue: {
    fontSize: TYPOGRAPHY.fontSize.lg,
    fontWeight: 'bold',
    color: COLORS.primary,
  },
  checkoutButton: {
    marginTop: SPACING.sm,
  },
  emptyCart: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: SPACING.xl,
  },
  emptyCartTitle: {
    fontSize: TYPOGRAPHY.fontSize.xl,
    fontWeight: 'bold',
    color: COLORS.textPrimary,
    marginTop: SPACING.lg,
    marginBottom: SPACING.sm,
  },
  emptyCartSubtitle: {
    fontSize: TYPOGRAPHY.fontSize.md,
    color: COLORS.textSecondary,
    textAlign: 'center',
    marginBottom: SPACING.xl,
  },
  browseButton: {
    paddingHorizontal: SPACING.xl,
  },
});
