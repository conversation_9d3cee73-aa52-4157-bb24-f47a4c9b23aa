import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { useLocalSearchParams } from 'expo-router';
import React, { useEffect, useRef, useState } from 'react';
import {
    Alert,
    Animated,
    FlatList,
    Image,
    Modal,
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { FoodWayButton } from '../../components/ui/FoodWayButton';
import { FoodWayFloatingCart } from '../../components/ui/FoodWayFloatingCart';
import { FoodWayMenuItem } from '../../components/ui/FoodWayMenuItem';
import { FoodWayStickyHeader } from '../../components/ui/FoodWayStickyHeader';
import { FoodWayText } from '../../components/ui/FoodWayText';
import { Colors } from '../../constants/Colors';
import { DesignSystem } from '../../constants/DesignSystem';
import { useCartStore } from '../../store/cartStore';
import { Category, MenuItem, Restaurant, SelectedAddOn, SelectedCustomization } from '../../types';

const { Spacing, BorderRadius, Shadows, Typography } = DesignSystem;

// Mock restaurant data
const mockRestaurant: Restaurant = {
  id: '1',
  name: 'Mario\'s Pizza Palace',
  description: 'Authentic Italian pizza made with fresh ingredients and traditional recipes passed down through generations.',
  image: 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=400&h=300&fit=crop',
  coverImage: 'https://images.unsplash.com/photo-1555396273-367ea4eb4db5?w=400&h=200&fit=crop',
  cuisine: ['Italian', 'Pizza'],
  rating: 4.5,
  reviewCount: 324,
  deliveryTime: '25-35 min',
  deliveryFee: 2.99,
  minimumOrder: 15,
  isOpen: true,
  address: '123 Main St, Downtown',
  latitude: 37.7749,
  longitude: -122.4194,
  phone: '******-0123',
  categories: [
    { id: 'cat1', name: 'Pizza', restaurantId: '1' },
    { id: 'cat2', name: 'Appetizers', restaurantId: '1' },
    { id: 'cat3', name: 'Salads', restaurantId: '1' },
    { id: 'cat4', name: 'Desserts', restaurantId: '1' },
  ],
  featured: true,
  promoted: false,
  tags: ['Popular', 'Fast Delivery'],
};

const mockMenuItems: MenuItem[] = [
  {
    id: 'item1',
    restaurantId: '1',
    categoryId: 'cat1',
    name: 'Margherita Pizza',
    description: 'Classic pizza with fresh mozzarella, tomato sauce, and basil',
    image: 'https://images.unsplash.com/photo-1604382354936-07c5d9983bd3?w=300&h=200&fit=crop',
    price: 16.99,
    isAvailable: true,
    isVegetarian: true,
    isVegan: false,
    isGlutenFree: false,
    isSpicy: false,
    calories: 280,
    preparationTime: '15-20 min',
    customizations: [
      {
        id: 'size',
        name: 'Size',
        type: 'single',
        required: true,
        options: [
          { id: 'small', name: 'Small (10")', price: 0, isDefault: true },
          { id: 'medium', name: 'Medium (12")', price: 3 },
          { id: 'large', name: 'Large (14")', price: 6 },
        ],
      },
      {
        id: 'crust',
        name: 'Crust Type',
        type: 'single',
        required: true,
        options: [
          { id: 'thin', name: 'Thin Crust', price: 0, isDefault: true },
          { id: 'thick', name: 'Thick Crust', price: 2 },
          { id: 'stuffed', name: 'Stuffed Crust', price: 4 },
        ],
      },
    ],
    addOns: [
      { id: 'extra-cheese', name: 'Extra Cheese', price: 2.50 },
      { id: 'pepperoni', name: 'Pepperoni', price: 3.00 },
      { id: 'mushrooms', name: 'Mushrooms', price: 1.50 },
    ],
    tags: ['Popular', 'Vegetarian'],
  },
  {
    id: 'item2',
    restaurantId: '1',
    categoryId: 'cat2',
    name: 'Garlic Bread',
    description: 'Crispy bread with garlic butter and herbs',
    image: 'https://images.unsplash.com/photo-1573140247632-f8fd74997d5c?w=300&h=200&fit=crop',
    price: 6.99,
    isAvailable: true,
    isVegetarian: true,
    isVegan: false,
    isGlutenFree: false,
    isSpicy: false,
    calories: 180,
    preparationTime: '8-10 min',
    customizations: [],
    addOns: [
      { id: 'cheese-dip', name: 'Cheese Dip', price: 1.50 },
      { id: 'marinara', name: 'Marinara Sauce', price: 1.00 },
    ],
    tags: ['Appetizer'],
  },
];

export default function RestaurantScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const [restaurant] = useState<Restaurant>(mockRestaurant);
  const [menuItems] = useState<MenuItem[]>(mockMenuItems);
  const [selectedCategory, setSelectedCategory] = useState<string>('cat1');
  const [selectedItem, setSelectedItem] = useState<MenuItem | null>(null);
  const [showItemModal, setShowItemModal] = useState(false);
  const [customizations, setCustomizations] = useState<SelectedCustomization[]>([]);
  const [addOns, setAddOns] = useState<SelectedAddOn[]>([]);
  const [quantity, setQuantity] = useState(1);
  const [specialInstructions, setSpecialInstructions] = useState('');
  const [isFavorite, setIsFavorite] = useState(false);
  const [scrollY] = useState(new Animated.Value(0));

  const { addItem, setRestaurant, items, getTotalAmount, getTotalItems } = useCartStore();

  // Modern animations
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(30)).current;
  const headerOpacity = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 600,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  useEffect(() => {
    setRestaurant(restaurant);
  }, [restaurant, setRestaurant]);

  const filteredMenuItems = menuItems.filter(item => item.categoryId === selectedCategory);

  const handleItemPress = (item: MenuItem) => {
    setSelectedItem(item);
    setCustomizations([]);
    setAddOns([]);
    setQuantity(1);
    setSpecialInstructions('');
    setShowItemModal(true);
  };

  const handleAddToCart = () => {
    if (!selectedItem) return;

    // Validate required customizations
    const requiredCustomizations = selectedItem.customizations.filter(c => c.required);
    const selectedCustomizationIds = customizations.map(c => c.customizationId);

    for (const required of requiredCustomizations) {
      if (!selectedCustomizationIds.includes(required.id)) {
        Alert.alert('Required Selection', `Please select ${required.name}`);
        return;
      }
    }

    for (let i = 0; i < quantity; i++) {
      addItem(selectedItem, customizations, addOns, specialInstructions);
    }

    setShowItemModal(false);
    Alert.alert('Added to Cart', `${selectedItem.name} has been added to your cart`);
  };

  const handleBackPress = () => {
    router.back();
  };

  const handleFavoritePress = () => {
    setIsFavorite(!isFavorite);
  };

  const handleSharePress = () => {
    // Implement share functionality
    Alert.alert('Share', 'Share functionality would be implemented here');
  };

  const handleCartPress = () => {
    router.push('/(tabs)/cart');
  };

  const renderCategoryTab = (category: Category) => (
    <TouchableOpacity
      key={category.id}
      style={[
        styles.categoryTab,
        selectedCategory === category.id && styles.categoryTabActive,
      ]}
      onPress={() => setSelectedCategory(category.id)}
      activeOpacity={0.8}
    >
      <Text
        style={[
          styles.categoryTabText,
          selectedCategory === category.id && styles.categoryTabTextActive,
        ]}
      >
        {category.name}
      </Text>
    </TouchableOpacity>
  );



  return (
    <View style={styles.container}>
      {/* Sticky Header */}
      <FoodWayStickyHeader
        title={restaurant.name}
        subtitle={`${restaurant.cuisine.join(', ')} • ${restaurant.deliveryTime}`}
        onBackPress={handleBackPress}
        onFavoritePress={handleFavoritePress}
        onSharePress={handleSharePress}
        isFavorite={isFavorite}
        opacity={scrollY.interpolate({
          inputRange: [0, 200],
          outputRange: [0, 1],
          extrapolate: 'clamp',
        })}
      />

      <Animated.ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        scrollEventThrottle={16}
        onScroll={Animated.event(
          [{ nativeEvent: { contentOffset: { y: scrollY } } }],
          { useNativeDriver: false }
        )}
      >
        {/* Hero Image Section */}
        <View style={styles.heroSection}>
          <Image source={{ uri: restaurant.coverImage }} style={styles.heroImage} />
          <LinearGradient
            colors={['transparent', 'rgba(0,0,0,0.7)']}
            style={styles.heroGradient}
          />
          <View style={styles.heroOverlay}>
            <TouchableOpacity style={styles.overlayButton} onPress={handleBackPress}>
              <Ionicons name="arrow-back" size={24} color={Colors.white} />
            </TouchableOpacity>
            <View style={styles.overlayActions}>
              <TouchableOpacity style={styles.overlayButton} onPress={handleSharePress}>
                <Ionicons name="share-outline" size={22} color={Colors.white} />
              </TouchableOpacity>
              <TouchableOpacity style={styles.overlayButton} onPress={handleFavoritePress}>
                <Ionicons
                  name={isFavorite ? "heart" : "heart-outline"}
                  size={22}
                  color={isFavorite ? Colors.error : Colors.white}
                />
              </TouchableOpacity>
            </View>
          </View>
        </View>

        {/* Restaurant Info Section */}
        <Animated.View style={[styles.restaurantInfo, { opacity: fadeAnim }]}>
          <View style={styles.restaurantHeader}>
            <FoodWayText variant="h2" style={styles.restaurantName}>
              {restaurant.name}
            </FoodWayText>
            <View style={styles.ratingContainer}>
              <Ionicons name="star" size={16} color={Colors.rating} />
              <Text style={styles.ratingText}>{restaurant.rating}</Text>
              <Text style={styles.reviewText}>({restaurant.reviewCount})</Text>
            </View>
          </View>

          <FoodWayText variant="body" color="textSecondary" style={styles.description}>
            {restaurant.description}
          </FoodWayText>

          <View style={styles.metaRow}>
            <View style={styles.metaItem}>
              <Ionicons name="time-outline" size={16} color={Colors.primary} />
              <Text style={styles.metaText}>{restaurant.deliveryTime}</Text>
            </View>
            <View style={styles.metaItem}>
              <Ionicons name="bicycle-outline" size={16} color={Colors.primary} />
              <Text style={styles.metaText}>${restaurant.deliveryFee.toFixed(2)} delivery</Text>
            </View>
            <View style={styles.metaItem}>
              <Ionicons name="wallet-outline" size={16} color={Colors.primary} />
              <Text style={styles.metaText}>${restaurant.minimumOrder} min</Text>
            </View>
          </View>

          <View style={styles.cuisineTags}>
            {restaurant.cuisine.map((cuisine, index) => (
              <View key={index} style={styles.cuisineTag}>
                <Text style={styles.cuisineTagText}>{cuisine}</Text>
              </View>
            ))}
          </View>
        </Animated.View>

        {/* Category Tabs */}
        <View style={styles.categorySection}>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.categoryTabs}
          >
            {restaurant.categories.map(renderCategoryTab)}
          </ScrollView>
        </View>

        {/* Menu Items */}
        <Animated.View style={[styles.menuSection, { opacity: fadeAnim }]}>
          <FlatList
            data={filteredMenuItems}
            renderItem={({ item }) => (
              <FoodWayMenuItem
                item={item}
                onPress={handleItemPress}
                onAddPress={(menuItem) => {
                  // Quick add for simple items, or open modal for complex items
                  if (menuItem.customizations.length === 0 && menuItem.addOns.length === 0) {
                    addItem(menuItem, [], [], '');
                    Alert.alert('Added to Cart', `${menuItem.name} has been added to your cart`);
                  } else {
                    handleItemPress(menuItem);
                  }
                }}
              />
            )}
            keyExtractor={(item) => item.id}
            scrollEnabled={false}
            contentContainerStyle={styles.menuList}
          />
        </Animated.View>
      </Animated.ScrollView>

      {/* Floating Cart */}
      <FoodWayFloatingCart
        itemCount={getTotalItems()}
        totalAmount={getTotalAmount()}
        onPress={handleCartPress}
        visible={getTotalItems() > 0}
      />

      {/* Item Customization Modal */}
      <Modal
        visible={showItemModal}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setShowItemModal(false)}>
              <Ionicons name="close" size={24} color={Colors.textPrimary} />
            </TouchableOpacity>
            <Text style={styles.modalTitle}>Customize Item</Text>
            <View style={{ width: 24 }} />
          </View>

          {selectedItem && (
            <ScrollView style={styles.modalContent}>
              <Image source={{ uri: selectedItem.image }} style={styles.modalItemImage} />
              <Text style={styles.modalItemName}>{selectedItem.name}</Text>
              <Text style={styles.modalItemDescription}>{selectedItem.description}</Text>
              <Text style={styles.modalItemPrice}>${selectedItem.price.toFixed(2)}</Text>

              {/* Customizations */}
              {selectedItem.customizations.map((customization) => (
                <View key={customization.id} style={styles.customizationSection}>
                  <Text style={styles.customizationTitle}>
                    {customization.name} {customization.required && '*'}
                  </Text>
                  {customization.options.map((option) => (
                    <TouchableOpacity
                      key={option.id}
                      style={styles.customizationOption}
                      onPress={() => {
                        if (customization.type === 'single') {
                          setCustomizations(prev => [
                            ...prev.filter(c => c.customizationId !== customization.id),
                            { customizationId: customization.id, optionIds: [option.id] }
                          ]);
                        }
                      }}
                    >
                      <View style={styles.optionLeft}>
                        <View style={[
                          styles.radioButton,
                          customizations.some(c => 
                            c.customizationId === customization.id && 
                            c.optionIds.includes(option.id)
                          ) && styles.radioButtonSelected
                        ]} />
                        <Text style={styles.optionName}>{option.name}</Text>
                      </View>
                      {option.price > 0 && (
                        <Text style={styles.optionPrice}>+${option.price.toFixed(2)}</Text>
                      )}
                    </TouchableOpacity>
                  ))}
                </View>
              ))}

              {/* Add-ons */}
              {selectedItem.addOns.length > 0 && (
                <View style={styles.addOnsSection}>
                  <Text style={styles.addOnsTitle}>Add-ons</Text>
                  {selectedItem.addOns.map((addOn) => (
                    <TouchableOpacity
                      key={addOn.id}
                      style={styles.addOnOption}
                      onPress={() => {
                        const existing = addOns.find(a => a.addOnId === addOn.id);
                        if (existing) {
                          setAddOns(prev => prev.filter(a => a.addOnId !== addOn.id));
                        } else {
                          setAddOns(prev => [...prev, { addOnId: addOn.id, quantity: 1 }]);
                        }
                      }}
                    >
                      <View style={styles.optionLeft}>
                        <View style={[
                          styles.checkbox,
                          addOns.some(a => a.addOnId === addOn.id) && styles.checkboxSelected
                        ]}>
                          {addOns.some(a => a.addOnId === addOn.id) && (
                            <Ionicons name="checkmark" size={16} color={Colors.white} />
                          )}
                        </View>
                        <Text style={styles.optionName}>{addOn.name}</Text>
                      </View>
                      <Text style={styles.optionPrice}>+${addOn.price.toFixed(2)}</Text>
                    </TouchableOpacity>
                  ))}
                </View>
              )}

              {/* Quantity */}
              <View style={styles.quantitySection}>
                <Text style={styles.quantityTitle}>Quantity</Text>
                <View style={styles.quantityControls}>
                  <TouchableOpacity
                    style={styles.quantityButton}
                    onPress={() => setQuantity(Math.max(1, quantity - 1))}
                  >
                    <Ionicons name="remove" size={20} color={Colors.primary} />
                  </TouchableOpacity>
                  <Text style={styles.quantityText}>{quantity}</Text>
                  <TouchableOpacity
                    style={styles.quantityButton}
                    onPress={() => setQuantity(quantity + 1)}
                  >
                    <Ionicons name="add" size={20} color={Colors.primary} />
                  </TouchableOpacity>
                </View>
              </View>
            </ScrollView>
          )}

          <View style={styles.modalFooter}>
            <FoodWayButton
              title="Add to Cart"
              onPress={handleAddToCart}
              fullWidth
            />
          </View>
        </SafeAreaView>
      </Modal>
    </View>
  );
}

// Updated styles using new design system
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  scrollView: {
    flex: 1,
  },
  heroSection: {
    position: 'relative',
    height: 280,
  },
  heroImage: {
    width: '100%',
    height: '100%',
  },
  heroGradient: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 120,
  },
  heroOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: Spacing.lg,
    paddingTop: Spacing.xl,
  },
  overlayButton: {
    width: 40,
    height: 40,
    borderRadius: BorderRadius.full,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  overlayActions: {
    flexDirection: 'row',
    gap: Spacing.sm,
  },
  restaurantInfo: {
    backgroundColor: Colors.white,
    padding: Spacing.lg,
    marginBottom: Spacing.sm,
  },
  restaurantHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: Spacing.sm,
  },
  restaurantName: {
    flex: 1,
    marginRight: Spacing.md,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.warningAlpha,
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    borderRadius: BorderRadius.md,
    gap: 2,
  },
  ratingText: {
    fontSize: Typography.fontSize.sm,
    fontFamily: Typography.fontFamily.semibold,
    fontWeight: Typography.fontWeight.semibold,
    color: Colors.rating,
  },
  reviewText: {
    fontSize: Typography.fontSize.xs,
    fontFamily: Typography.fontFamily.regular,
    color: Colors.textSecondary,
  },
  description: {
    marginBottom: Spacing.md,
    lineHeight: Typography.fontSize.base * Typography.lineHeight.relaxed,
  },
  metaRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: Spacing.md,
  },
  metaItem: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    gap: 4,
  },
  metaText: {
    fontSize: Typography.fontSize.sm,
    fontFamily: Typography.fontFamily.medium,
    fontWeight: Typography.fontWeight.medium,
    color: Colors.textSecondary,
  },
  cuisineTags: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: Spacing.xs,
  },
  cuisineTag: {
    backgroundColor: Colors.primaryAlpha,
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    borderRadius: BorderRadius.md,
  },
  cuisineTagText: {
    fontSize: Typography.fontSize.sm,
    fontFamily: Typography.fontFamily.medium,
    fontWeight: Typography.fontWeight.medium,
    color: Colors.primary,
  },
  categorySection: {
    backgroundColor: Colors.white,
    paddingVertical: Spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  categoryTabs: {
    paddingHorizontal: Spacing.lg,
    gap: Spacing.sm,
  },
  categoryTab: {
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.sm,
    borderRadius: BorderRadius.full,
    backgroundColor: Colors.surface,
  },
  categoryTabActive: {
    backgroundColor: Colors.primary,
  },
  categoryTabText: {
    fontSize: Typography.fontSize.sm,
    fontFamily: Typography.fontFamily.semibold,
    fontWeight: Typography.fontWeight.semibold,
    color: Colors.textSecondary,
  },
  categoryTabTextActive: {
    color: Colors.white,
  },
  menuSection: {
    backgroundColor: Colors.white,
    paddingTop: Spacing.md,
  },
  menuList: {
    paddingHorizontal: Spacing.lg,
  },
  // Modal styles
  modalContainer: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
    backgroundColor: Colors.white,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  modalTitle: {
    fontSize: Typography.fontSize.lg,
    fontFamily: Typography.fontFamily.semibold,
    fontWeight: Typography.fontWeight.semibold,
    color: Colors.textPrimary,
  },
  modalContent: {
    flex: 1,
    backgroundColor: Colors.white,
    padding: Spacing.lg,
  },
  modalItemImage: {
    width: '100%',
    height: 200,
    borderRadius: BorderRadius.lg,
    marginBottom: Spacing.md,
  },
  modalItemName: {
    fontSize: Typography.fontSize.xl,
    fontFamily: Typography.fontFamily.bold,
    fontWeight: Typography.fontWeight.bold,
    color: Colors.textPrimary,
    marginBottom: Spacing.xs,
  },
  modalItemDescription: {
    fontSize: Typography.fontSize.base,
    fontFamily: Typography.fontFamily.regular,
    color: Colors.textSecondary,
    lineHeight: Typography.fontSize.base * Typography.lineHeight.relaxed,
    marginBottom: Spacing.sm,
  },
  modalItemPrice: {
    fontSize: Typography.fontSize.lg,
    fontFamily: Typography.fontFamily.bold,
    fontWeight: Typography.fontWeight.bold,
    color: Colors.primary,
    marginBottom: Spacing.lg,
  },
  customizationSection: {
    marginBottom: Spacing.lg,
  },
  customizationTitle: {
    fontSize: Typography.fontSize.base,
    fontFamily: Typography.fontFamily.semibold,
    fontWeight: Typography.fontWeight.semibold,
    color: Colors.textPrimary,
    marginBottom: Spacing.md,
  },
  customizationOption: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: Spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  optionLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  radioButton: {
    width: 20,
    height: 20,
    borderRadius: BorderRadius.full,
    borderWidth: 2,
    borderColor: Colors.border,
    marginRight: Spacing.sm,
  },
  radioButtonSelected: {
    borderColor: Colors.primary,
    backgroundColor: Colors.primary,
  },
  optionName: {
    fontSize: Typography.fontSize.base,
    fontFamily: Typography.fontFamily.regular,
    color: Colors.textPrimary,
    flex: 1,
  },
  optionPrice: {
    fontSize: Typography.fontSize.base,
    fontFamily: Typography.fontFamily.semibold,
    fontWeight: Typography.fontWeight.semibold,
    color: Colors.primary,
  },
  addOnsSection: {
    marginBottom: Spacing.lg,
  },
  addOnsTitle: {
    fontSize: Typography.fontSize.base,
    fontFamily: Typography.fontFamily.semibold,
    fontWeight: Typography.fontWeight.semibold,
    color: Colors.textPrimary,
    marginBottom: Spacing.md,
  },
  addOnOption: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: Spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  checkbox: {
    width: 20,
    height: 20,
    borderRadius: BorderRadius.sm,
    borderWidth: 2,
    borderColor: Colors.border,
    marginRight: Spacing.sm,
    alignItems: 'center',
    justifyContent: 'center',
  },
  checkboxSelected: {
    borderColor: Colors.primary,
    backgroundColor: Colors.primary,
  },
  quantitySection: {
    marginBottom: Spacing.lg,
  },
  quantityTitle: {
    fontSize: Typography.fontSize.base,
    fontFamily: Typography.fontFamily.semibold,
    fontWeight: Typography.fontWeight.semibold,
    color: Colors.textPrimary,
    marginBottom: Spacing.md,
  },
  quantityControls: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  quantityButton: {
    width: 40,
    height: 40,
    borderRadius: BorderRadius.full,
    backgroundColor: Colors.surface,
    alignItems: 'center',
    justifyContent: 'center',
  },
  quantityText: {
    fontSize: Typography.fontSize.lg,
    fontFamily: Typography.fontFamily.bold,
    fontWeight: Typography.fontWeight.bold,
    color: Colors.textPrimary,
    marginHorizontal: Spacing.lg,
    minWidth: 40,
    textAlign: 'center',
  },
  modalFooter: {
    backgroundColor: Colors.white,
    padding: Spacing.lg,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
  },
});
