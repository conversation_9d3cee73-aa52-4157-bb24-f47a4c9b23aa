/**
 * FoodWay Restaurant Card Component
 * Modern restaurant card with image, rating, delivery info, and offers
 */

import React from 'react';
import {
  View,
  Text,
  Image,
  TouchableOpacity,
  StyleSheet,
  ViewStyle,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { DesignSystem } from '../../constants/DesignSystem';
import { FoodWayBadge } from './FoodWayBadge';

const { Colors, Typography, Spacing, BorderRadius, Shadows, Layout } = DesignSystem;

interface Restaurant {
  id: string;
  name: string;
  image: string;
  rating: number;
  deliveryTime: string;
  deliveryFee: string;
  cuisine: string[];
  offer?: string;
  isVeg?: boolean;
}

interface FoodWayRestaurantCardProps {
  restaurant: Restaurant;
  onPress?: (restaurant: Restaurant) => void;
  style?: ViewStyle;
}

export const FoodWayRestaurantCard: React.FC<FoodWayRestaurantCardProps> = ({
  restaurant,
  onPress,
  style,
}) => {
  const handlePress = () => {
    onPress?.(restaurant);
  };

  return (
    <TouchableOpacity 
      style={[styles.container, style]} 
      onPress={handlePress}
      activeOpacity={0.8}
    >
      <View style={styles.imageContainer}>
        <Image 
          source={{ uri: restaurant.image }} 
          style={styles.image}
          resizeMode="cover"
        />
        
        {restaurant.offer && (
          <View style={styles.offerBadge}>
            <FoodWayBadge 
              text={restaurant.offer} 
              variant="warning" 
              size="small" 
            />
          </View>
        )}
        
        {restaurant.isVeg !== undefined && (
          <View style={styles.vegBadge}>
            <FoodWayBadge 
              text={restaurant.isVeg ? "VEG" : "NON-VEG"} 
              variant={restaurant.isVeg ? "veg" : "nonVeg"} 
              size="small" 
            />
          </View>
        )}
      </View>
      
      <View style={styles.content}>
        <Text style={styles.name} numberOfLines={1}>
          {restaurant.name}
        </Text>
        
        <Text style={styles.cuisine} numberOfLines={1}>
          {restaurant.cuisine.join(', ')}
        </Text>
        
        <View style={styles.infoRow}>
          <View style={styles.ratingContainer}>
            <Ionicons name="star" size={14} color={Colors.rating} />
            <Text style={styles.rating}>{restaurant.rating}</Text>
          </View>
          
          <View style={styles.separator} />
          
          <View style={styles.deliveryInfo}>
            <Ionicons name="time-outline" size={14} color={Colors.textSecondary} />
            <Text style={styles.deliveryTime}>{restaurant.deliveryTime}</Text>
          </View>
          
          <View style={styles.separator} />
          
          <Text style={styles.deliveryFee}>{restaurant.deliveryFee}</Text>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.white,
    borderRadius: BorderRadius.lg,
    overflow: 'hidden',
    ...Shadows.sm,
    marginBottom: Spacing.md,
  },
  
  imageContainer: {
    position: 'relative',
    height: 160,
  },
  
  image: {
    width: '100%',
    height: '100%',
  },
  
  offerBadge: {
    position: 'absolute',
    top: Spacing.sm,
    left: Spacing.sm,
  },
  
  vegBadge: {
    position: 'absolute',
    top: Spacing.sm,
    right: Spacing.sm,
  },
  
  content: {
    padding: Spacing.md,
  },
  
  name: {
    fontSize: Typography.fontSize.lg,
    fontFamily: Typography.fontFamily.semibold,
    fontWeight: Typography.fontWeight.semibold,
    color: Colors.textPrimary,
    marginBottom: Spacing.xs,
  },
  
  cuisine: {
    fontSize: Typography.fontSize.sm,
    fontFamily: Typography.fontFamily.regular,
    color: Colors.textSecondary,
    marginBottom: Spacing.sm,
  },
  
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 2,
  },
  
  rating: {
    fontSize: Typography.fontSize.sm,
    fontFamily: Typography.fontFamily.semibold,
    fontWeight: Typography.fontWeight.semibold,
    color: Colors.rating,
  },
  
  separator: {
    width: 1,
    height: 12,
    backgroundColor: Colors.border,
    marginHorizontal: Spacing.sm,
  },
  
  deliveryInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 2,
  },
  
  deliveryTime: {
    fontSize: Typography.fontSize.sm,
    fontFamily: Typography.fontFamily.regular,
    color: Colors.textSecondary,
  },
  
  deliveryFee: {
    fontSize: Typography.fontSize.sm,
    fontFamily: Typography.fontFamily.regular,
    color: Colors.textSecondary,
  },
});

export default FoodWayRestaurantCard;
