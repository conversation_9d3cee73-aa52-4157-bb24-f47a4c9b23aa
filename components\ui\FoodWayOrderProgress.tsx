/**
 * FoodWay Order Progress Component
 * Modern progress indicator with prominent icons and animations
 */

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  Animated,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { DesignSystem } from '../../constants/DesignSystem';
import { FoodWayText } from './FoodWayText';

const { Colors, Typography, Spacing, BorderRadius } = DesignSystem;

export interface OrderStep {
  id: string;
  title: string;
  description: string;
  icon: keyof typeof Ionicons.glyphMap;
  status: 'completed' | 'current' | 'pending';
  timestamp?: string;
}

interface FoodWayOrderProgressProps {
  steps: OrderStep[];
  currentStep: number;
}

export const FoodWayOrderProgress: React.FC<FoodWayOrderProgressProps> = ({
  steps,
  currentStep,
}) => {
  const renderStep = (step: OrderStep, index: number) => {
    const isCompleted = step.status === 'completed';
    const isCurrent = step.status === 'current';
    const isPending = step.status === 'pending';

    const iconColor = isCompleted 
      ? Colors.success 
      : isCurrent 
        ? Colors.primary 
        : Colors.textLight;

    const iconBackgroundColor = isCompleted 
      ? Colors.successAlpha 
      : isCurrent 
        ? Colors.primaryAlpha 
        : Colors.surface;

    return (
      <View key={step.id} style={styles.stepContainer}>
        {/* Progress Line */}
        {index > 0 && (
          <View style={[
            styles.progressLine,
            { backgroundColor: isCompleted ? Colors.success : Colors.border }
          ]} />
        )}
        
        {/* Step Content */}
        <View style={styles.stepContent}>
          {/* Icon Container */}
          <View style={[
            styles.iconContainer,
            { backgroundColor: iconBackgroundColor }
          ]}>
            {isCompleted ? (
              <Ionicons 
                name="checkmark-circle" 
                size={32} 
                color={Colors.success} 
              />
            ) : (
              <Ionicons 
                name={step.icon} 
                size={24} 
                color={iconColor} 
              />
            )}
          </View>

          {/* Step Details */}
          <View style={styles.stepDetails}>
            <FoodWayText 
              variant="h4" 
              color={isCurrent ? "primary" : isCompleted ? "success" : "textSecondary"}
              style={styles.stepTitle}
            >
              {step.title}
            </FoodWayText>
            
            <FoodWayText 
              variant="bodySmall" 
              color="textSecondary"
              style={styles.stepDescription}
            >
              {step.description}
            </FoodWayText>
            
            {step.timestamp && (
              <FoodWayText 
                variant="caption" 
                color="textLight"
                style={styles.stepTimestamp}
              >
                {step.timestamp}
              </FoodWayText>
            )}
          </View>
        </View>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <FoodWayText variant="h3" style={styles.title}>
        Order Progress
      </FoodWayText>
      
      <View style={styles.progressContainer}>
        {steps.map(renderStep)}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.white,
    padding: Spacing.lg,
    borderRadius: BorderRadius.lg,
    marginBottom: Spacing.md,
  },
  
  title: {
    marginBottom: Spacing.lg,
  },
  
  progressContainer: {
    position: 'relative',
  },
  
  stepContainer: {
    position: 'relative',
    marginBottom: Spacing.lg,
  },
  
  progressLine: {
    position: 'absolute',
    left: 24,
    top: -Spacing.lg,
    width: 2,
    height: Spacing.lg,
  },
  
  stepContent: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: BorderRadius.full,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: Spacing.md,
  },
  
  stepDetails: {
    flex: 1,
    paddingTop: 4,
  },
  
  stepTitle: {
    marginBottom: Spacing.xs,
  },
  
  stepDescription: {
    marginBottom: Spacing.xs,
    lineHeight: Typography.fontSize.sm * Typography.lineHeight.normal,
  },
  
  stepTimestamp: {
    fontStyle: 'italic',
  },
});

export default FoodWayOrderProgress;
