import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import * as Location from 'expo-location';
import { router } from 'expo-router';
import React, { useEffect, useRef, useState } from 'react';
import {
    Animated,
    FlatList,
    Image,
    RefreshControl,
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { FoodWayMiniLogo } from '../../components/FoodWayLogo';
import { FoodWayRestaurantCard } from '../../components/ui/FoodWayRestaurantCard';
import { FoodWaySearchBar } from '../../components/ui/FoodWaySearchBar';
import { FoodWayText } from '../../components/ui/FoodWayText';
import { Colors } from '../../constants/Colors';
import { DesignSystem } from '../../constants/DesignSystem';
import { useAuthStore } from '../../store/authStore';
import { Category, Restaurant } from '../../types';
import { useCleanup } from '../../utils/memoryManager';
import { usePerformanceMonitor } from '../../utils/performanceMonitor';

const { Spacing, BorderRadius, Shadows, Typography, Layout } = DesignSystem;

// Using centralized mock data service
// Temporary fallback data while imports are being resolved
const fallbackCategories: Category[] = [
  { id: '1', name: 'Pizza', image: 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=100&h=100&fit=crop' },
  { id: '2', name: 'Burgers', image: 'https://images.unsplash.com/photo-1568901346375-23c9450c58cd?w=100&h=100&fit=crop' },
  { id: '3', name: 'Sushi', image: 'https://images.unsplash.com/photo-1579584425555-c3ce17fd4351?w=100&h=100&fit=crop' },
  { id: '4', name: 'Mexican', image: 'https://images.unsplash.com/photo-1565299507177-b0ac66763828?w=100&h=100&fit=crop' },
];

const fallbackRestaurants: Restaurant[] = [
  {
    id: '1',
    name: 'Mario\'s Pizza Palace',
    description: 'Authentic Italian pizza made with fresh ingredients',
    image: 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=300&h=200&fit=crop',
    cuisine: ['Italian', 'Pizza'],
    rating: 4.5,
    reviewCount: 324,
    deliveryTime: '25-35 min',
    deliveryFee: 2.99,
    minimumOrder: 15,
    isOpen: true,
    address: '123 Main St, Downtown',
    latitude: 37.7749,
    longitude: -122.4194,
    phone: '******-0123',
    categories: [],
    featured: true,
    promoted: false,
    tags: ['Popular', 'Fast Delivery'],
  },
  {
    id: '2',
    name: 'Burger Junction',
    description: 'Gourmet burgers and crispy fries',
    image: 'https://images.unsplash.com/photo-1568901346375-23c9450c58cd?w=300&h=200&fit=crop',
    cuisine: ['American', 'Burgers'],
    rating: 4.3,
    reviewCount: 189,
    deliveryTime: '20-30 min',
    deliveryFee: 1.99,
    minimumOrder: 12,
    isOpen: true,
    address: '456 Oak Ave, Midtown',
    latitude: 37.7849,
    longitude: -122.4094,
    phone: '******-0124',
    categories: [],
    featured: false,
    promoted: true,
    tags: ['New', 'Highly Rated'],
  },
];

// Quick Actions Data
const quickActions = [
  { id: '1', name: 'Restaurants', icon: 'restaurant', route: '/(tabs)/search?type=restaurants' },
  { id: '2', name: 'Groceries', icon: 'basket', route: '/(tabs)/search?type=groceries' },
  { id: '3', name: 'Pharmacy', icon: 'medical', route: '/(tabs)/search?type=pharmacy' },
  { id: '4', name: 'More', icon: 'grid', route: '/(tabs)/search' },
];



export default function HomeScreen() {
  const [categories, setCategories] = useState<Category[]>([]);
  const [restaurants, setRestaurants] = useState<Restaurant[]>([]);
  const [featuredRestaurants, setFeaturedRestaurants] = useState<Restaurant[]>([]);
  const [refreshing, setRefreshing] = useState(false);
  const [currentLocation, setCurrentLocation] = useState<string>('Getting location...');
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');

  // Performance monitoring
  const { measureAsync, startRender, endRender } = usePerformanceMonitor('HomeScreen');
  const { addCleanup } = useCleanup();

  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;

  const { user } = useAuthStore();

  useEffect(() => {
    startRender();

    // Start animations
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 600,
        useNativeDriver: true,
      }),
    ]).start();

    // Load initial data
    loadData();
    getCurrentLocation();

    // Cleanup function
    addCleanup(() => {
      // Any cleanup needed for this component
    });

    endRender();
  }, []);

  const getCurrentLocation = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        setCurrentLocation('Location access denied');
        return;
      }

      const location = await Location.getCurrentPositionAsync({});
      const address = await Location.reverseGeocodeAsync({
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
      });

      if (address[0]) {
        const { street, city, region } = address[0];
        setCurrentLocation(`${street || ''} ${city || ''}, ${region || ''}`.trim());
      }
    } catch (error) {
      console.error('Error getting location:', error);
      setCurrentLocation('Unable to get location');
    }
  };

  const loadData = async () => {
    await measureAsync('loadData', async () => {
      try {
        setLoading(true);

        try {
          // Try to load from mock API service
          const { mockApiService } = await import('../../services/mock/mockApiService');

          const categoriesResponse = await measureAsync('loadCategories', async () =>
            await mockApiService.restaurants.getCategories()
          );

          if (categoriesResponse.success) {
            setCategories(categoriesResponse.data || []);
          }

          // Load restaurants from mock API
          const restaurantsResponse = await measureAsync('loadRestaurants', async () =>
            await mockApiService.restaurants.getRestaurants()
          );

          if (restaurantsResponse.success) {
            const allRestaurants = restaurantsResponse.data || [];
            setRestaurants(allRestaurants);

            // Filter featured restaurants
            const featured = allRestaurants.filter(restaurant => restaurant.featured);
            setFeaturedRestaurants(featured);
          }
        } catch (importError) {
          console.warn('Failed to load from mock API service, using fallback data:', importError);
          // Use fallback data if import fails
          setCategories(fallbackCategories);
          setRestaurants(fallbackRestaurants);
          const featured = fallbackRestaurants.filter(restaurant => restaurant.featured);
          setFeaturedRestaurants(featured);
        }
      } catch (error) {
        console.error('Error loading data:', error);
        // Use fallback data as last resort
        setCategories(fallbackCategories);
        setRestaurants(fallbackRestaurants);
        const featured = fallbackRestaurants.filter(restaurant => restaurant.featured);
        setFeaturedRestaurants(featured);
      } finally {
        setLoading(false);
      }
    });
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadData();
    await getCurrentLocation();
    setRefreshing(false);
  };

  const handleSearch = (query: string) => {
    if (query.trim()) {
      router.push(`/(tabs)/search?query=${encodeURIComponent(query)}`);
    }
  };

  const handleFilterPress = () => {
    router.push('/(tabs)/search?showFilters=true');
  };

  const renderCategory = ({ item }: { item: Category }) => (
    <TouchableOpacity
      style={styles.categoryItem}
      onPress={() => router.push(`/(tabs)/search?category=${item.name}`)}
    >
      <View style={styles.categoryImageContainer}>
        <Image source={{ uri: item.image }} style={styles.categoryImage} />
      </View>
      <Text style={styles.categoryText}>{item.name}</Text>
    </TouchableOpacity>
  );



  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Modern Header with Location and Search */}
        <LinearGradient
          colors={[Colors.gradientStart, Colors.gradientEnd]}
          style={styles.header}
        >
          <Animated.View
            style={[
              styles.headerContent,
              {
                opacity: fadeAnim,
                transform: [{ translateY: slideAnim }],
              },
            ]}
          >
            {/* Header Top Row */}
            <View style={styles.headerTop}>
              <FoodWayMiniLogo size={35} color={Colors.white} />
              <TouchableOpacity style={styles.profileButton}>
                <Ionicons name="person-circle" size={32} color={Colors.white} />
              </TouchableOpacity>
            </View>

            {/* Location Selector */}
            <TouchableOpacity style={styles.locationSelector}>
              <Ionicons name="location" size={20} color={Colors.white} />
              <View style={styles.locationTextContainer}>
                <Text style={styles.deliverToText}>Deliver to</Text>
                <Text style={styles.locationText} numberOfLines={1}>
                  {currentLocation}
                </Text>
              </View>
              <Ionicons name="chevron-down" size={16} color={Colors.white} />
            </TouchableOpacity>

            {/* Welcome Message */}
            {user && (
              <View style={styles.welcomeContainer}>
                <Text style={styles.welcomeText}>
                  What would you like to eat today, {user.firstName || 'User'}?
                </Text>
              </View>
            )}
          </Animated.View>
        </LinearGradient>

        {/* Search Bar Section */}
        <View style={styles.searchSection}>
          <FoodWaySearchBar
            placeholder="Search restaurants, food..."
            value={searchQuery}
            onChangeText={setSearchQuery}
            onSearch={handleSearch}
            onFilterPress={handleFilterPress}
          />
        </View>

        {/* Quick Actions Grid */}
        <Animated.View
          style={[
            styles.quickActionsSection,
            {
              opacity: fadeAnim,
              transform: [{ translateY: slideAnim }],
            },
          ]}
        >
          <View style={styles.quickActionsGrid}>
            {quickActions.map((action) => (
              <TouchableOpacity
                key={action.id}
                style={styles.quickActionItem}
                onPress={() => router.push(action.route as any)}
              >
                <View style={styles.quickActionIcon}>
                  <Ionicons
                    name={action.icon as any}
                    size={24}
                    color={Colors.primary}
                  />
                </View>
                <Text style={styles.quickActionText}>{action.name}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </Animated.View>

        {/* Categories Section */}
        <Animated.View
          style={[
            styles.section,
            {
              opacity: fadeAnim,
              transform: [{ translateY: slideAnim }],
            },
          ]}
        >
          <FoodWayText variant="h3" style={styles.sectionTitle}>
            Browse Categories
          </FoodWayText>
          <FlatList
            data={categories}
            renderItem={renderCategory}
            keyExtractor={(item) => item.id}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.categoriesList}
            // Performance optimizations
            removeClippedSubviews={true}
            maxToRenderPerBatch={5}
            initialNumToRender={5}
            windowSize={10}
          />
        </Animated.View>

        {/* Featured Restaurants */}
        {featuredRestaurants.length > 0 && (
          <Animated.View
            style={[
              styles.section,
              {
                opacity: fadeAnim,
                transform: [{ translateY: slideAnim }],
              },
            ]}
          >
            <FoodWayText variant="h3" style={styles.sectionTitle}>
              Featured Near You
            </FoodWayText>
            <FlatList
              data={featuredRestaurants}
              renderItem={({ item }) => (
                <FoodWayRestaurantCard
                  restaurant={{
                    id: item.id,
                    name: item.name,
                    image: item.image,
                    rating: item.rating,
                    deliveryTime: item.deliveryTime,
                    deliveryFee: `$${item.deliveryFee.toFixed(2)}`,
                    cuisine: item.cuisine,
                    offer: item.promoted ? "PROMOTED" : undefined,
                  }}
                  onPress={(restaurant) => router.push(`/restaurant/${restaurant.id}`)}
                  style={styles.featuredRestaurantCard}
                />
              )}
              keyExtractor={(item) => item.id}
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.restaurantsList}
              // Performance optimizations
              removeClippedSubviews={true}
              maxToRenderPerBatch={3}
              initialNumToRender={2}
              windowSize={5}
            />
          </Animated.View>
        )}

        {/* All Restaurants */}
        <Animated.View
          style={[
            styles.section,
            {
              opacity: fadeAnim,
              transform: [{ translateY: slideAnim }],
            },
          ]}
        >
          <FoodWayText variant="h3" style={styles.sectionTitle}>
            All Restaurants
          </FoodWayText>
          {restaurants.map((restaurant) => (
            <FoodWayRestaurantCard
              key={restaurant.id}
              restaurant={{
                id: restaurant.id,
                name: restaurant.name,
                image: restaurant.image,
                rating: restaurant.rating,
                deliveryTime: restaurant.deliveryTime,
                deliveryFee: `$${restaurant.deliveryFee.toFixed(2)}`,
                cuisine: restaurant.cuisine,
                offer: restaurant.promoted ? "PROMOTED" : undefined,
              }}
              onPress={(restaurant) => router.push(`/restaurant/${restaurant.id}`)}
              style={styles.restaurantListCard}
            />
          ))}
        </Animated.View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    paddingHorizontal: Spacing.lg,
    paddingTop: Spacing.md,
    paddingBottom: Spacing.xl,
    borderBottomLeftRadius: BorderRadius.xl,
    borderBottomRightRadius: BorderRadius.xl,
  },
  headerContent: {
    gap: Spacing.md,
  },
  headerTop: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  profileButton: {
    padding: Spacing.xs,
  },
  locationSelector: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.sm,
    paddingVertical: Spacing.sm,
  },
  locationTextContainer: {
    flex: 1,
  },
  deliverToText: {
    fontSize: Typography.fontSize.xs,
    fontFamily: Typography.fontFamily.regular,
    color: Colors.white,
    opacity: 0.9,
  },
  locationText: {
    fontSize: Typography.fontSize.base,
    fontFamily: Typography.fontFamily.semibold,
    fontWeight: Typography.fontWeight.semibold,
    color: Colors.white,
  },
  welcomeContainer: {
    marginTop: Spacing.sm,
  },
  welcomeText: {
    fontSize: Typography.fontSize.lg,
    fontFamily: Typography.fontFamily.medium,
    fontWeight: Typography.fontWeight.medium,
    color: Colors.white,
    lineHeight: Typography.fontSize.lg * Typography.lineHeight.normal,
  },
  searchSection: {
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
    backgroundColor: Colors.background,
  },
  quickActionsSection: {
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
  },
  quickActionsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  quickActionItem: {
    alignItems: 'center',
    flex: 1,
    paddingVertical: Spacing.md,
  },
  quickActionIcon: {
    width: 56,
    height: 56,
    borderRadius: BorderRadius.lg,
    backgroundColor: Colors.primaryAlpha,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: Spacing.sm,
  },
  quickActionText: {
    fontSize: Typography.fontSize.sm,
    fontFamily: Typography.fontFamily.medium,
    fontWeight: Typography.fontWeight.medium,
    color: Colors.textPrimary,
    textAlign: 'center',
  },
  section: {
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.lg,
  },
  sectionTitle: {
    marginBottom: Spacing.md,
  },
  categoriesList: {
    paddingRight: Spacing.lg,
  },
  categoryItem: {
    alignItems: 'center',
    marginRight: Spacing.md,
    width: 80,
  },
  categoryImageContainer: {
    width: 64,
    height: 64,
    borderRadius: BorderRadius.lg,
    overflow: 'hidden',
    marginBottom: Spacing.xs,
    ...Shadows.sm,
  },
  categoryImage: {
    width: '100%',
    height: '100%',
  },
  categoryText: {
    fontSize: Typography.fontSize.sm,
    fontFamily: Typography.fontFamily.medium,
    fontWeight: Typography.fontWeight.medium,
    color: Colors.textPrimary,
    textAlign: 'center',
  },
  restaurantsList: {
    paddingRight: Spacing.lg,
  },
  featuredRestaurantCard: {
    marginRight: Spacing.md,
    width: 280,
  },
  restaurantListCard: {
    marginBottom: Spacing.md,
  },
});
