import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import * as Location from 'expo-location';
import { router } from 'expo-router';
import React, { useEffect, useRef, useState } from 'react';
import {
    Animated,
    FlatList,
    Image,
    RefreshControl,
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { FoodWayMiniLogo } from '../../components/FoodWayLogo';
import { useAuthStore } from '../../store/authStore';
import { Category, Restaurant } from '../../types';
import { BORDER_RADIUS, COLORS, SPACING, TYPOGRAPHY } from '../../utils/constants';
import { useCleanup } from '../../utils/memoryManager';
import { usePerformanceMonitor } from '../../utils/performanceMonitor';

// Using centralized mock data service
// Temporary fallback data while imports are being resolved
const fallbackCategories: Category[] = [
  { id: '1', name: 'Pizza', image: 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=100&h=100&fit=crop' },
  { id: '2', name: 'Burgers', image: 'https://images.unsplash.com/photo-1568901346375-23c9450c58cd?w=100&h=100&fit=crop' },
  { id: '3', name: 'Sushi', image: 'https://images.unsplash.com/photo-1579584425555-c3ce17fd4351?w=100&h=100&fit=crop' },
  { id: '4', name: 'Mexican', image: 'https://images.unsplash.com/photo-1565299507177-b0ac66763828?w=100&h=100&fit=crop' },
];

const fallbackRestaurants: Restaurant[] = [
  {
    id: '1',
    name: 'Mario\'s Pizza Palace',
    description: 'Authentic Italian pizza made with fresh ingredients',
    image: 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=300&h=200&fit=crop',
    cuisine: ['Italian', 'Pizza'],
    rating: 4.5,
    reviewCount: 324,
    deliveryTime: '25-35 min',
    deliveryFee: 2.99,
    minimumOrder: 15,
    isOpen: true,
    address: '123 Main St, Downtown',
    latitude: 37.7749,
    longitude: -122.4194,
    phone: '******-0123',
    categories: [],
    featured: true,
    promoted: false,
    tags: ['Popular', 'Fast Delivery'],
  },
  {
    id: '2',
    name: 'Burger Junction',
    description: 'Gourmet burgers and crispy fries',
    image: 'https://images.unsplash.com/photo-1568901346375-23c9450c58cd?w=300&h=200&fit=crop',
    cuisine: ['American', 'Burgers'],
    rating: 4.3,
    reviewCount: 189,
    deliveryTime: '20-30 min',
    deliveryFee: 1.99,
    minimumOrder: 12,
    isOpen: true,
    address: '456 Oak Ave, Midtown',
    latitude: 37.7849,
    longitude: -122.4094,
    phone: '******-0124',
    categories: [],
    featured: false,
    promoted: true,
    tags: ['New', 'Highly Rated'],
  },
];



export default function HomeScreen() {
  const [categories, setCategories] = useState<Category[]>([]);
  const [restaurants, setRestaurants] = useState<Restaurant[]>([]);
  const [featuredRestaurants, setFeaturedRestaurants] = useState<Restaurant[]>([]);
  const [refreshing, setRefreshing] = useState(false);
  const [currentLocation, setCurrentLocation] = useState<string>('Getting location...');
  const [loading, setLoading] = useState(true);

  // Performance monitoring
  const { measureAsync, startRender, endRender } = usePerformanceMonitor('HomeScreen');
  const { addCleanup } = useCleanup();

  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;

  const { user } = useAuthStore();

  useEffect(() => {
    startRender();

    // Start animations
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 600,
        useNativeDriver: true,
      }),
    ]).start();

    // Load initial data
    loadData();
    getCurrentLocation();

    // Cleanup function
    addCleanup(() => {
      // Any cleanup needed for this component
    });

    endRender();
  }, []);

  const getCurrentLocation = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        setCurrentLocation('Location access denied');
        return;
      }

      const location = await Location.getCurrentPositionAsync({});
      const address = await Location.reverseGeocodeAsync({
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
      });

      if (address[0]) {
        const { street, city, region } = address[0];
        setCurrentLocation(`${street || ''} ${city || ''}, ${region || ''}`.trim());
      }
    } catch (error) {
      console.error('Error getting location:', error);
      setCurrentLocation('Unable to get location');
    }
  };

  const loadData = async () => {
    await measureAsync('loadData', async () => {
      try {
        setLoading(true);

        try {
          // Try to load from mock API service
          const { mockApiService } = await import('../../services/mock/mockApiService');

          const categoriesResponse = await measureAsync('loadCategories', async () =>
            await mockApiService.restaurants.getCategories()
          );

          if (categoriesResponse.success) {
            setCategories(categoriesResponse.data || []);
          }

          // Load restaurants from mock API
          const restaurantsResponse = await measureAsync('loadRestaurants', async () =>
            await mockApiService.restaurants.getRestaurants()
          );

          if (restaurantsResponse.success) {
            const allRestaurants = restaurantsResponse.data || [];
            setRestaurants(allRestaurants);

            // Filter featured restaurants
            const featured = allRestaurants.filter(restaurant => restaurant.featured);
            setFeaturedRestaurants(featured);
          }
        } catch (importError) {
          console.warn('Failed to load from mock API service, using fallback data:', importError);
          // Use fallback data if import fails
          setCategories(fallbackCategories);
          setRestaurants(fallbackRestaurants);
          const featured = fallbackRestaurants.filter(restaurant => restaurant.featured);
          setFeaturedRestaurants(featured);
        }
      } catch (error) {
        console.error('Error loading data:', error);
        // Use fallback data as last resort
        setCategories(fallbackCategories);
        setRestaurants(fallbackRestaurants);
        const featured = fallbackRestaurants.filter(restaurant => restaurant.featured);
        setFeaturedRestaurants(featured);
      } finally {
        setLoading(false);
      }
    });
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadData();
    await getCurrentLocation();
    setRefreshing(false);
  };

  const renderCategory = ({ item }: { item: Category }) => (
    <TouchableOpacity
      style={styles.categoryItem}
      onPress={() => router.push(`/(tabs)/search?category=${item.name}`)}
    >
      <View style={styles.categoryImageContainer}>
        <Image source={{ uri: item.image }} style={styles.categoryImage} />
      </View>
      <Text style={styles.categoryText}>{item.name}</Text>
    </TouchableOpacity>
  );

  const renderRestaurant = ({ item }: { item: Restaurant }) => (
    <TouchableOpacity
      style={styles.restaurantCard}
      onPress={() => router.push(`/restaurant/${item.id}`)}
    >
      <View style={styles.restaurantImageContainer}>
        <Image source={{ uri: item.image }} style={styles.restaurantImage} />
        {!item.isOpen && (
          <View style={styles.closedOverlay}>
            <Text style={styles.closedText}>Closed</Text>
          </View>
        )}
      </View>
      <View style={styles.restaurantInfo}>
        <Text style={styles.restaurantName}>{item.name}</Text>
        <Text style={styles.restaurantDescription} numberOfLines={2}>
          {item.description}
        </Text>
        <View style={styles.restaurantMeta}>
          <View style={styles.ratingContainer}>
            <Ionicons name="star" size={14} color={COLORS.warning} />
            <Text style={styles.rating}>{item.rating}</Text>
            <Text style={styles.reviewCount}>({item.reviewCount})</Text>
          </View>
          <Text style={styles.deliveryTime}>{item.deliveryTime}</Text>
        </View>
        <View style={styles.deliveryInfo}>
          <Text style={styles.deliveryFee}>
            Delivery: ${item.deliveryFee.toFixed(2)}
          </Text>
          <Text style={styles.minimumOrder}>
            Min: ${item.minimumOrder}
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Header with Gradient */}
        <LinearGradient
          colors={[COLORS.primary, COLORS.primaryDark]}
          style={styles.header}
        >
          <Animated.View
            style={[
              styles.headerContent,
              {
                opacity: fadeAnim,
                transform: [{ translateY: slideAnim }],
              },
            ]}
          >
            <View style={styles.headerTop}>
              <FoodWayMiniLogo size={35} color={COLORS.white} />
              <View style={styles.locationContainer}>
                <Ionicons name="location" size={20} color={COLORS.white} />
                <View style={styles.locationTextContainer}>
                  <Text style={styles.deliverToText}>Deliver to</Text>
                  <Text style={styles.locationText} numberOfLines={1}>
                    {currentLocation}
                  </Text>
                </View>
              </View>
            </View>

            {user && (
              <View style={styles.welcomeContainer}>
                <Text style={styles.welcomeText}>
                  Welcome back, {user.firstName || 'User'}!
                </Text>
              </View>
            )}
          </Animated.View>
        </LinearGradient>

        {/* Categories Section */}
        <Animated.View
          style={[
            styles.section,
            {
              opacity: fadeAnim,
              transform: [{ translateY: slideAnim }],
            },
          ]}
        >
          <Text style={styles.sectionTitle}>Categories</Text>
          <FlatList
            data={categories}
            renderItem={renderCategory}
            keyExtractor={(item) => item.id}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.categoriesList}
            // Performance optimizations
            removeClippedSubviews={true}
            maxToRenderPerBatch={5}
            initialNumToRender={5}
            windowSize={10}
          />
        </Animated.View>

        {/* Featured Restaurants */}
        {featuredRestaurants.length > 0 && (
          <Animated.View
            style={[
              styles.section,
              {
                opacity: fadeAnim,
                transform: [{ translateY: slideAnim }],
              },
            ]}
          >
            <Text style={styles.sectionTitle}>Featured</Text>
            <FlatList
              data={featuredRestaurants}
              renderItem={renderRestaurant}
              keyExtractor={(item) => item.id}
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.restaurantsList}
              // Performance optimizations
              removeClippedSubviews={true}
              maxToRenderPerBatch={3}
              initialNumToRender={2}
              windowSize={5}
            />
          </Animated.View>
        )}

        {/* All Restaurants */}
        <Animated.View
          style={[
            styles.section,
            {
              opacity: fadeAnim,
              transform: [{ translateY: slideAnim }],
            },
          ]}
        >
          <Text style={styles.sectionTitle}>All Restaurants</Text>
          {restaurants.map((restaurant) => (
            <View key={restaurant.id} style={styles.restaurantListItem}>
              {renderRestaurant({ item: restaurant })}
            </View>
          ))}
        </Animated.View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    paddingHorizontal: SPACING.lg,
    paddingTop: SPACING.md,
    paddingBottom: SPACING.xl,
    borderBottomLeftRadius: BORDER_RADIUS.xl,
    borderBottomRightRadius: BORDER_RADIUS.xl,
  },
  headerContent: {
    gap: SPACING.md,
  },
  headerTop: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: SPACING.sm,
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: SPACING.sm,
  },
  locationTextContainer: {
    flex: 1,
  },
  deliverToText: {
    ...TYPOGRAPHY.caption,
    color: COLORS.white,
    opacity: 0.9,
  },
  locationText: {
    ...TYPOGRAPHY.body,
    color: COLORS.white,
    fontWeight: '600',
  },
  welcomeContainer: {
    marginTop: SPACING.sm,
  },
  welcomeText: {
    ...TYPOGRAPHY.h3,
    color: COLORS.white,
    fontWeight: '700',
  },
  section: {
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.lg,
  },
  sectionTitle: {
    ...TYPOGRAPHY.h2,
    color: COLORS.text,
    fontWeight: '700',
    marginBottom: SPACING.md,
  },
  categoriesList: {
    paddingRight: SPACING.lg,
  },
  categoryItem: {
    alignItems: 'center',
    marginRight: SPACING.md,
    width: 80,
  },
  categoryImageContainer: {
    width: 60,
    height: 60,
    borderRadius: BORDER_RADIUS.lg,
    overflow: 'hidden',
    marginBottom: SPACING.xs,
    shadowColor: COLORS.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  categoryImage: {
    width: '100%',
    height: '100%',
  },
  categoryText: {
    ...TYPOGRAPHY.caption,
    color: COLORS.text,
    fontWeight: '600',
    textAlign: 'center',
  },
  restaurantsList: {
    paddingRight: SPACING.lg,
  },
  restaurantCard: {
    backgroundColor: COLORS.white,
    borderRadius: BORDER_RADIUS.lg,
    marginRight: SPACING.md,
    width: 280,
    shadowColor: COLORS.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
    overflow: 'hidden',
  },
  restaurantListItem: {
    marginBottom: SPACING.md,
  },
  restaurantImageContainer: {
    position: 'relative',
  },
  restaurantImage: {
    width: '100%',
    height: 160,
  },
  closedOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  closedText: {
    ...TYPOGRAPHY.body,
    color: COLORS.white,
    fontWeight: '700',
  },
  restaurantInfo: {
    padding: SPACING.md,
  },
  restaurantName: {
    ...TYPOGRAPHY.h3,
    color: COLORS.text,
    fontWeight: '700',
    marginBottom: SPACING.xs,
  },
  restaurantDescription: {
    ...TYPOGRAPHY.body,
    color: COLORS.textSecondary,
    marginBottom: SPACING.sm,
    lineHeight: 20,
  },
  restaurantMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.xs,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  rating: {
    ...TYPOGRAPHY.caption,
    color: COLORS.text,
    fontWeight: '600',
  },
  reviewCount: {
    ...TYPOGRAPHY.caption,
    color: COLORS.textSecondary,
  },
  deliveryTime: {
    ...TYPOGRAPHY.caption,
    color: COLORS.textSecondary,
    fontWeight: '500',
  },
  deliveryInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  deliveryFee: {
    ...TYPOGRAPHY.caption,
    color: COLORS.success,
    fontWeight: '600',
  },
  minimumOrder: {
    ...TYPOGRAPHY.caption,
    color: COLORS.textSecondary,
  },
});
