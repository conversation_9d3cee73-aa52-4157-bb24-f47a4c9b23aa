/**
 * FoodWay Address Selection Screen
 * Modern address selection with prominent icons and location features
 */

import React, { useState, useRef } from 'react';
import {
  View,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Animated,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { 
  FoodWayText,
  FoodWayCard,
  FoodWayButton,
  FoodWayInput,
  FoodWayStickyHeader,
  FoodWayBadge,
  Colors,
  DesignSystem 
} from '../../components/ui';

const { Spacing, BorderRadius, Typography, Shadows } = DesignSystem;

// <PERSON><PERSON> saved addresses with prominent icons
const savedAddresses = [
  {
    id: '1',
    type: 'home',
    label: 'Home',
    address: '123 Main Street, Apt 4B',
    city: 'New York, NY 10001',
    icon: 'home' as const,
    color: Colors.success,
    isDefault: true,
  },
  {
    id: '2',
    type: 'work',
    label: 'Work',
    address: '456 Business Ave, Suite 200',
    city: 'New York, NY 10002',
    icon: 'business' as const,
    color: Colors.info,
    isDefault: false,
  },
  {
    id: '3',
    type: 'other',
    label: 'Mom\'s House',
    address: '789 Family Road',
    city: 'Brooklyn, NY 11201',
    icon: 'heart' as const,
    color: Colors.error,
    isDefault: false,
  },
];

export default function AddressScreen() {
  const [selectedAddress, setSelectedAddress] = useState(savedAddresses[0].id);
  const [showAddForm, setShowAddForm] = useState(false);
  const [newAddress, setNewAddress] = useState({
    label: '',
    address: '',
    city: '',
    instructions: '',
  });
  
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;

  React.useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 500,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 500,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  const handleAddressSelect = (addressId: string) => {
    setSelectedAddress(addressId);
  };

  const handleCurrentLocation = () => {
    Alert.alert(
      'Use Current Location',
      'We\'ll detect your current location for delivery.',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Allow', onPress: () => console.log('Location access granted') },
      ]
    );
  };

  const handleAddNewAddress = () => {
    setShowAddForm(true);
  };

  const handleSaveAddress = () => {
    // Validate and save new address
    if (!newAddress.label || !newAddress.address || !newAddress.city) {
      Alert.alert('Error', 'Please fill in all required fields');
      return;
    }
    
    Alert.alert('Success', 'Address saved successfully!');
    setShowAddForm(false);
    setNewAddress({ label: '', address: '', city: '', instructions: '' });
  };

  const handleContinue = () => {
    router.push('/checkout/payment');
  };

  const renderAddressCard = (address: typeof savedAddresses[0]) => (
    <TouchableOpacity
      key={address.id}
      onPress={() => handleAddressSelect(address.id)}
      activeOpacity={0.8}
    >
      <FoodWayCard 
        variant={selectedAddress === address.id ? "elevated" : "default"}
        style={[
          styles.addressCard,
          selectedAddress === address.id && styles.selectedAddressCard,
        ]}
      >
        <View style={styles.addressContent}>
          <View style={[styles.addressIcon, { backgroundColor: `${address.color}20` }]}>
            <Ionicons name={address.icon} size={24} color={address.color} />
          </View>
          
          <View style={styles.addressInfo}>
            <View style={styles.addressHeader}>
              <FoodWayText variant="h4" style={styles.addressLabel}>
                {address.label}
              </FoodWayText>
              {address.isDefault && (
                <FoodWayBadge text="Default" variant="success" style={styles.defaultBadge} />
              )}
            </View>
            <FoodWayText variant="body" color="textPrimary" style={styles.addressText}>
              {address.address}
            </FoodWayText>
            <FoodWayText variant="bodySmall" color="textSecondary">
              {address.city}
            </FoodWayText>
          </View>
          
          <View style={styles.radioButton}>
            {selectedAddress === address.id && (
              <View style={styles.radioButtonSelected}>
                <Ionicons name="checkmark" size={12} color={Colors.white} />
              </View>
            )}
          </View>
        </View>
      </FoodWayCard>
    </TouchableOpacity>
  );

  const renderAddForm = () => {
    if (!showAddForm) return null;

    return (
      <Animated.View
        style={[
          styles.addFormContainer,
          {
            opacity: fadeAnim,
            transform: [{ translateY: slideAnim }],
          },
        ]}
      >
        <FoodWayCard variant="elevated" style={styles.addForm}>
          <FoodWayText variant="h3" style={styles.addFormTitle}>
            Add New Address
          </FoodWayText>
          
          <FoodWayInput
            label="Address Label"
            placeholder="e.g., Home, Work, Friend's Place"
            value={newAddress.label}
            onChangeText={(text) => setNewAddress({ ...newAddress, label: text })}
            leftIcon={<Ionicons name="bookmark" size={20} color={Colors.textSecondary} />}
            style={styles.formInput}
          />
          
          <FoodWayInput
            label="Street Address"
            placeholder="123 Main Street, Apt 4B"
            value={newAddress.address}
            onChangeText={(text) => setNewAddress({ ...newAddress, address: text })}
            leftIcon={<Ionicons name="location" size={20} color={Colors.textSecondary} />}
            style={styles.formInput}
          />
          
          <FoodWayInput
            label="City, State, ZIP"
            placeholder="New York, NY 10001"
            value={newAddress.city}
            onChangeText={(text) => setNewAddress({ ...newAddress, city: text })}
            leftIcon={<Ionicons name="business" size={20} color={Colors.textSecondary} />}
            style={styles.formInput}
          />
          
          <FoodWayInput
            label="Delivery Instructions (Optional)"
            placeholder="Ring doorbell, Leave at door, etc."
            value={newAddress.instructions}
            onChangeText={(text) => setNewAddress({ ...newAddress, instructions: text })}
            leftIcon={<Ionicons name="chatbubble" size={20} color={Colors.textSecondary} />}
            multiline
            numberOfLines={3}
            style={styles.formInput}
          />
          
          <View style={styles.formButtons}>
            <FoodWayButton
              title="Cancel"
              variant="outline"
              onPress={() => setShowAddForm(false)}
              style={styles.formButton}
            />
            <FoodWayButton
              title="Save Address"
              variant="primary"
              onPress={handleSaveAddress}
              style={styles.formButton}
            />
          </View>
        </FoodWayCard>
      </Animated.View>
    );
  };

  return (
    <View style={styles.container}>
      <FoodWayStickyHeader
        title="Delivery Address"
        subtitle="Where should we deliver your order?"
        onBackPress={() => router.back()}
      />

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <Animated.View style={[styles.content, { opacity: fadeAnim }]}>
          {/* Current Location Option */}
          <FoodWayCard variant="elevated" style={styles.currentLocationCard}>
            <TouchableOpacity
              onPress={handleCurrentLocation}
              style={styles.currentLocationContent}
              activeOpacity={0.8}
            >
              <View style={styles.currentLocationIcon}>
                <Ionicons name="navigate" size={24} color={Colors.primary} />
              </View>
              <View style={styles.currentLocationInfo}>
                <FoodWayText variant="h4" color="primary">
                  Use Current Location
                </FoodWayText>
                <FoodWayText variant="bodySmall" color="textSecondary">
                  We'll detect your location automatically
                </FoodWayText>
              </View>
              <Ionicons name="chevron-forward" size={20} color={Colors.textSecondary} />
            </TouchableOpacity>
          </FoodWayCard>

          {/* Saved Addresses */}
          <View style={styles.savedAddressesSection}>
            <FoodWayText variant="h3" style={styles.sectionTitle}>
              Saved Addresses
            </FoodWayText>
            {savedAddresses.map(renderAddressCard)}
          </View>

          {/* Add New Address Button */}
          <FoodWayButton
            title="Add New Address"
            variant="outline"
            leftIcon={<Ionicons name="add" size={18} color={Colors.primary} />}
            onPress={handleAddNewAddress}
            style={styles.addAddressButton}
          />

          {/* Add Form */}
          {renderAddForm()}

          {/* Continue Button */}
          <FoodWayButton
            title="Continue to Payment"
            variant="primary"
            size="large"
            fullWidth
            leftIcon={<Ionicons name="arrow-forward" size={20} color={Colors.white} />}
            onPress={handleContinue}
            style={styles.continueButton}
          />

          <View style={styles.bottomSpacing} />
        </Animated.View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },

  scrollView: {
    flex: 1,
  },

  content: {
    paddingTop: 100, // Account for sticky header
  },

  currentLocationCard: {
    marginHorizontal: Spacing.lg,
    marginBottom: Spacing.lg,
  },

  currentLocationContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },

  currentLocationIcon: {
    width: 48,
    height: 48,
    borderRadius: BorderRadius.full,
    backgroundColor: Colors.primaryAlpha,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: Spacing.md,
  },

  currentLocationInfo: {
    flex: 1,
  },

  savedAddressesSection: {
    marginHorizontal: Spacing.lg,
    marginBottom: Spacing.lg,
  },

  sectionTitle: {
    marginBottom: Spacing.md,
  },

  addressCard: {
    marginBottom: Spacing.sm,
  },

  selectedAddressCard: {
    borderColor: Colors.primary,
    borderWidth: 2,
  },

  addressContent: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },

  addressIcon: {
    width: 48,
    height: 48,
    borderRadius: BorderRadius.full,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: Spacing.md,
  },

  addressInfo: {
    flex: 1,
  },

  addressHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.xs,
  },

  addressLabel: {
    flex: 1,
  },

  defaultBadge: {
    marginLeft: Spacing.sm,
  },

  addressText: {
    marginBottom: 2,
  },

  radioButton: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: Colors.border,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 4,
  },

  radioButtonSelected: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: Colors.primary,
    alignItems: 'center',
    justifyContent: 'center',
  },

  addAddressButton: {
    marginHorizontal: Spacing.lg,
    marginBottom: Spacing.lg,
  },

  addFormContainer: {
    marginHorizontal: Spacing.lg,
    marginBottom: Spacing.lg,
  },

  addForm: {
    padding: Spacing.lg,
  },

  addFormTitle: {
    marginBottom: Spacing.lg,
  },

  formInput: {
    marginBottom: Spacing.md,
  },

  formButtons: {
    flexDirection: 'row',
    gap: Spacing.md,
    marginTop: Spacing.md,
  },

  formButton: {
    flex: 1,
  },

  continueButton: {
    marginHorizontal: Spacing.lg,
    marginBottom: Spacing.lg,
  },

  bottomSpacing: {
    height: Spacing['6xl'],
  },
});
