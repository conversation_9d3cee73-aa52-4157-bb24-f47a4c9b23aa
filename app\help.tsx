/**
 * FoodWay Help & Support Screen
 * Modern help center with prominent icons and comprehensive support options
 */

import React, { useState, useRef } from 'react';
import {
  View,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Animated,
  Linking,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { 
  FoodWayText,
  FoodWayCard,
  FoodWayButton,
  FoodWayInput,
  FoodWayStickyHeader,
  FoodWaySearchBar,
  Colors,
  DesignSystem 
} from '../components/ui';

const { Spacing, BorderRadius, Typography, Shadows } = DesignSystem;

// FAQ categories with prominent icons
const faqCategories = [
  {
    id: 'orders',
    title: 'Orders & Delivery',
    icon: 'bag' as const,
    color: Colors.primary,
    count: 12,
    faqs: [
      {
        question: 'How can I track my order?',
        answer: 'You can track your order in real-time from the Orders tab or by clicking the tracking link in your confirmation email.',
      },
      {
        question: 'What if my order is late?',
        answer: 'If your order is significantly delayed, you can contact the restaurant directly or reach out to our support team for assistance.',
      },
      {
        question: 'Can I cancel my order?',
        answer: 'Orders can be cancelled within 2 minutes of placing them. After that, please contact the restaurant directly.',
      },
    ],
  },
  {
    id: 'payments',
    title: 'Payments & Billing',
    icon: 'card' as const,
    color: Colors.warning,
    count: 8,
    faqs: [
      {
        question: 'What payment methods do you accept?',
        answer: 'We accept all major credit cards, PayPal, Apple Pay, Google Pay, and cash on delivery.',
      },
      {
        question: 'How do refunds work?',
        answer: 'Refunds are processed within 3-5 business days to your original payment method.',
      },
    ],
  },
  {
    id: 'account',
    title: 'Account & Profile',
    icon: 'person' as const,
    color: Colors.info,
    count: 6,
    faqs: [
      {
        question: 'How do I update my profile?',
        answer: 'Go to Profile > Personal Information to update your details.',
      },
      {
        question: 'How do I change my password?',
        answer: 'You can change your password in Profile > Security Settings.',
      },
    ],
  },
  {
    id: 'technical',
    title: 'Technical Issues',
    icon: 'construct' as const,
    color: Colors.error,
    count: 5,
    faqs: [
      {
        question: 'The app is not working properly',
        answer: 'Try restarting the app or updating to the latest version. If issues persist, contact support.',
      },
    ],
  },
];

// Contact options with prominent icons
const contactOptions = [
  {
    id: 'live-chat',
    title: 'Live Chat',
    subtitle: 'Chat with our support team',
    icon: 'chatbubbles' as const,
    color: Colors.success,
    available: true,
    action: () => console.log('Open live chat'),
  },
  {
    id: 'phone',
    title: 'Call Support',
    subtitle: '+1 (800) FOODWAY',
    icon: 'call' as const,
    color: Colors.primary,
    available: true,
    action: () => Linking.openURL('tel:+18003663929'),
  },
  {
    id: 'email',
    title: 'Email Support',
    subtitle: '<EMAIL>',
    icon: 'mail' as const,
    color: Colors.info,
    available: true,
    action: () => Linking.openURL('mailto:<EMAIL>'),
  },
  {
    id: 'whatsapp',
    title: 'WhatsApp',
    subtitle: 'Message us on WhatsApp',
    icon: 'logo-whatsapp' as const,
    color: '#25D366',
    available: false,
    action: () => console.log('Open WhatsApp'),
  },
];

export default function HelpScreen() {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [expandedFAQ, setExpandedFAQ] = useState<string | null>(null);
  const fadeAnim = useRef(new Animated.Value(0)).current;

  React.useEffect(() => {
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 500,
      useNativeDriver: true,
    }).start();
  }, []);

  const filteredFAQs = faqCategories
    .filter(category => !selectedCategory || category.id === selectedCategory)
    .flatMap(category => 
      category.faqs
        .filter(faq => 
          faq.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
          faq.answer.toLowerCase().includes(searchQuery.toLowerCase())
        )
        .map(faq => ({ ...faq, category: category.title, categoryId: category.id }))
    );

  const handleCategoryPress = (categoryId: string) => {
    setSelectedCategory(selectedCategory === categoryId ? null : categoryId);
  };

  const handleFAQPress = (faqId: string) => {
    setExpandedFAQ(expandedFAQ === faqId ? null : faqId);
  };

  const renderCategoryCard = (category: typeof faqCategories[0]) => (
    <TouchableOpacity
      key={category.id}
      onPress={() => handleCategoryPress(category.id)}
      activeOpacity={0.8}
    >
      <FoodWayCard 
        variant={selectedCategory === category.id ? "elevated" : "default"}
        style={[
          styles.categoryCard,
          selectedCategory === category.id && styles.selectedCategoryCard,
        ]}
      >
        <View style={styles.categoryContent}>
          <View style={[styles.categoryIcon, { backgroundColor: `${category.color}20` }]}>
            <Ionicons name={category.icon} size={24} color={category.color} />
          </View>
          
          <View style={styles.categoryInfo}>
            <FoodWayText variant="h4" style={styles.categoryTitle}>
              {category.title}
            </FoodWayText>
            <FoodWayText variant="bodySmall" color="textSecondary">
              {category.count} articles
            </FoodWayText>
          </View>
          
          <Ionicons 
            name={selectedCategory === category.id ? "chevron-up" : "chevron-forward"} 
            size={20} 
            color={Colors.textSecondary} 
          />
        </View>
      </FoodWayCard>
    </TouchableOpacity>
  );

  const renderFAQItem = (faq: any, index: number) => {
    const faqId = `${faq.categoryId}-${index}`;
    const isExpanded = expandedFAQ === faqId;
    
    return (
      <FoodWayCard key={faqId} variant="default" style={styles.faqCard}>
        <TouchableOpacity
          onPress={() => handleFAQPress(faqId)}
          activeOpacity={0.8}
        >
          <View style={styles.faqHeader}>
            <View style={styles.faqQuestion}>
              <FoodWayText variant="body" style={styles.faqQuestionText}>
                {faq.question}
              </FoodWayText>
              {faq.category && (
                <FoodWayText variant="caption" color="textLight" style={styles.faqCategory}>
                  {faq.category}
                </FoodWayText>
              )}
            </View>
            <Ionicons 
              name={isExpanded ? "chevron-up" : "chevron-down"} 
              size={20} 
              color={Colors.textSecondary} 
            />
          </View>
        </TouchableOpacity>
        
        {isExpanded && (
          <View style={styles.faqAnswer}>
            <FoodWayText variant="body" color="textSecondary">
              {faq.answer}
            </FoodWayText>
          </View>
        )}
      </FoodWayCard>
    );
  };

  const renderContactOption = (option: typeof contactOptions[0]) => (
    <TouchableOpacity
      key={option.id}
      onPress={option.action}
      activeOpacity={0.8}
      disabled={!option.available}
    >
      <FoodWayCard 
        variant="elevated" 
        style={[
          styles.contactCard,
          !option.available && styles.contactCardDisabled,
        ]}
      >
        <View style={styles.contactContent}>
          <View style={[styles.contactIcon, { backgroundColor: `${option.color}20` }]}>
            <Ionicons name={option.icon} size={24} color={option.color} />
          </View>
          
          <View style={styles.contactInfo}>
            <FoodWayText variant="h4" style={styles.contactTitle}>
              {option.title}
            </FoodWayText>
            <FoodWayText variant="bodySmall" color="textSecondary">
              {option.subtitle}
            </FoodWayText>
          </View>
          
          {option.available ? (
            <Ionicons name="chevron-forward" size={20} color={Colors.textSecondary} />
          ) : (
            <FoodWayText variant="caption" color="textLight">
              Soon
            </FoodWayText>
          )}
        </View>
      </FoodWayCard>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <FoodWayStickyHeader
        title="Help & Support"
        subtitle="We're here to help you"
        onBackPress={() => router.back()}
      />

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <Animated.View style={[styles.content, { opacity: fadeAnim }]}>
          {/* Search Bar */}
          <View style={styles.searchContainer}>
            <FoodWaySearchBar
              placeholder="Search for help..."
              value={searchQuery}
              onChangeText={setSearchQuery}
              showFilter={false}
            />
          </View>

          {/* Quick Contact */}
          <View style={styles.quickContactSection}>
            <FoodWayText variant="h3" style={styles.sectionTitle}>
              Contact Support
            </FoodWayText>
            <View style={styles.contactGrid}>
              {contactOptions.map(renderContactOption)}
            </View>
          </View>

          {/* FAQ Categories */}
          {!searchQuery && (
            <View style={styles.categoriesSection}>
              <FoodWayText variant="h3" style={styles.sectionTitle}>
                Browse by Category
              </FoodWayText>
              {faqCategories.map(renderCategoryCard)}
            </View>
          )}

          {/* FAQ Results */}
          <View style={styles.faqSection}>
            <FoodWayText variant="h3" style={styles.sectionTitle}>
              {searchQuery ? 'Search Results' : 'Frequently Asked Questions'}
            </FoodWayText>
            
            {filteredFAQs.length === 0 ? (
              <FoodWayCard variant="elevated" style={styles.emptyState}>
                <View style={styles.emptyStateContent}>
                  <Ionicons name="search" size={60} color={Colors.textLight} />
                  <FoodWayText variant="h4" style={styles.emptyStateTitle}>
                    No results found
                  </FoodWayText>
                  <FoodWayText variant="body" color="textSecondary" style={styles.emptyStateSubtitle}>
                    Try different keywords or contact support
                  </FoodWayText>
                </View>
              </FoodWayCard>
            ) : (
              filteredFAQs.map(renderFAQItem)
            )}
          </View>

          <View style={styles.bottomSpacing} />
        </Animated.View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },

  scrollView: {
    flex: 1,
  },

  content: {
    paddingTop: 100, // Account for sticky header
  },

  searchContainer: {
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
  },

  quickContactSection: {
    paddingHorizontal: Spacing.lg,
    marginBottom: Spacing.lg,
  },

  sectionTitle: {
    marginBottom: Spacing.md,
  },

  contactGrid: {
    gap: Spacing.sm,
  },

  contactCard: {
    marginBottom: Spacing.sm,
  },

  contactCardDisabled: {
    opacity: 0.6,
  },

  contactContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },

  contactIcon: {
    width: 48,
    height: 48,
    borderRadius: BorderRadius.full,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: Spacing.md,
  },

  contactInfo: {
    flex: 1,
  },

  contactTitle: {
    marginBottom: 2,
  },

  categoriesSection: {
    paddingHorizontal: Spacing.lg,
    marginBottom: Spacing.lg,
  },

  categoryCard: {
    marginBottom: Spacing.sm,
  },

  selectedCategoryCard: {
    borderColor: Colors.primary,
    borderWidth: 2,
  },

  categoryContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },

  categoryIcon: {
    width: 48,
    height: 48,
    borderRadius: BorderRadius.full,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: Spacing.md,
  },

  categoryInfo: {
    flex: 1,
  },

  categoryTitle: {
    marginBottom: 2,
  },

  faqSection: {
    paddingHorizontal: Spacing.lg,
    marginBottom: Spacing.lg,
  },

  faqCard: {
    marginBottom: Spacing.sm,
  },

  faqHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },

  faqQuestion: {
    flex: 1,
    marginRight: Spacing.md,
  },

  faqQuestionText: {
    marginBottom: Spacing.xs,
  },

  faqCategory: {
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },

  faqAnswer: {
    paddingTop: Spacing.md,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
    marginTop: Spacing.md,
  },

  emptyState: {
    marginTop: Spacing.lg,
  },

  emptyStateContent: {
    alignItems: 'center',
    paddingVertical: Spacing['2xl'],
  },

  emptyStateTitle: {
    marginTop: Spacing.md,
    marginBottom: Spacing.sm,
    textAlign: 'center',
  },

  emptyStateSubtitle: {
    textAlign: 'center',
  },

  bottomSpacing: {
    height: Spacing['6xl'],
  },
});
