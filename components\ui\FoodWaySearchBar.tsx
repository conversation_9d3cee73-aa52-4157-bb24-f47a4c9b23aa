/**
 * FoodWay Search Bar Component
 * Modern search input with icon and suggestions
 */

import React, { useState } from 'react';
import {
  View,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  ViewStyle,
  TextStyle,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { DesignSystem } from '../../constants/DesignSystem';

const { Colors, Typography, Spacing, BorderRadius, Shadows, Layout } = DesignSystem;

interface FoodWaySearchBarProps {
  placeholder?: string;
  value?: string;
  onChangeText?: (text: string) => void;
  onSearch?: (text: string) => void;
  onFocus?: () => void;
  onBlur?: () => void;
  showFilter?: boolean;
  onFilterPress?: () => void;
}

export const FoodWaySearchBar: React.FC<FoodWaySearchBarProps> = ({
  placeholder = "Search restaurants, food...",
  value,
  onChangeText,
  onSearch,
  onFocus,
  onBlur,
  showFilter = true,
  onFilterPress,
}) => {
  const [isFocused, setIsFocused] = useState(false);

  const handleFocus = () => {
    setIsFocused(true);
    onFocus?.();
  };

  const handleBlur = () => {
    setIsFocused(false);
    onBlur?.();
  };

  const handleSearch = () => {
    onSearch?.(value || '');
  };

  const containerStyles: ViewStyle[] = [
    styles.container,
    isFocused && styles.containerFocused,
  ];

  return (
    <View style={styles.wrapper}>
      <View style={containerStyles}>
        <TouchableOpacity onPress={handleSearch} style={styles.searchIcon}>
          <Ionicons 
            name="search" 
            size={20} 
            color={isFocused ? Colors.primary : Colors.textSecondary} 
          />
        </TouchableOpacity>
        
        <TextInput
          style={styles.input}
          placeholder={placeholder}
          placeholderTextColor={Colors.textLight}
          value={value}
          onChangeText={onChangeText}
          onFocus={handleFocus}
          onBlur={handleBlur}
          onSubmitEditing={handleSearch}
          returnKeyType="search"
        />
        
        {value && (
          <TouchableOpacity 
            onPress={() => onChangeText?.('')} 
            style={styles.clearIcon}
          >
            <Ionicons 
              name="close-circle" 
              size={20} 
              color={Colors.textSecondary} 
            />
          </TouchableOpacity>
        )}
      </View>
      
      {showFilter && (
        <TouchableOpacity onPress={onFilterPress} style={styles.filterButton}>
          <Ionicons name="options" size={20} color={Colors.white} />
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  wrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.sm,
  },
  
  container: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.surface,
    borderRadius: BorderRadius.lg,
    borderWidth: 1,
    borderColor: Colors.border,
    height: Layout.dimensions.inputHeight,
    paddingHorizontal: Spacing.md,
    ...Shadows.sm,
  },
  
  containerFocused: {
    borderColor: Colors.primary,
    borderWidth: 2,
    ...Shadows.md,
  },
  
  searchIcon: {
    marginRight: Spacing.sm,
  },
  
  input: {
    flex: 1,
    fontSize: Typography.fontSize.base,
    fontFamily: Typography.fontFamily.regular,
    color: Colors.textPrimary,
    height: '100%',
  },
  
  clearIcon: {
    marginLeft: Spacing.sm,
  },
  
  filterButton: {
    width: Layout.dimensions.inputHeight,
    height: Layout.dimensions.inputHeight,
    backgroundColor: Colors.primary,
    borderRadius: BorderRadius.lg,
    alignItems: 'center',
    justifyContent: 'center',
    ...Shadows.sm,
  },
});

export default FoodWaySearchBar;
