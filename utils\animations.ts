/**
 * FoodWay Animation Utilities
 * Reusable animation functions and configurations for consistent micro-interactions
 */

import { Animated, Easing } from 'react-native';
import { DesignSystem } from '../constants/DesignSystem';

const { Animations } = DesignSystem;

// === BASIC ANIMATIONS ===

/**
 * Scale animation for button press feedback
 */
export const createScaleAnimation = (
  animatedValue: Animated.Value,
  toValue: number = 0.95,
  duration: number = Animations.timing.fast
) => {
  return Animated.timing(animatedValue, {
    toValue,
    duration,
    easing: Easing.out(Easing.quad),
    useNativeDriver: true,
  });
};

/**
 * Fade animation for showing/hiding elements
 */
export const createFadeAnimation = (
  animatedValue: Animated.Value,
  toValue: number,
  duration: number = Animations.timing.normal
) => {
  return Animated.timing(animatedValue, {
    toValue,
    duration,
    easing: Easing.out(Easing.quad),
    useNativeDriver: true,
  });
};

/**
 * Slide animation for screen transitions
 */
export const createSlideAnimation = (
  animatedValue: Animated.Value,
  toValue: number,
  duration: number = Animations.timing.normal
) => {
  return Animated.timing(animatedValue, {
    toValue,
    duration,
    easing: Easing.out(Easing.cubic),
    useNativeDriver: true,
  });
};

/**
 * Spring animation for bouncy interactions
 */
export const createSpringAnimation = (
  animatedValue: Animated.Value,
  toValue: number,
  tension: number = Animations.spring.tension,
  friction: number = Animations.spring.friction
) => {
  return Animated.spring(animatedValue, {
    toValue,
    tension,
    friction,
    useNativeDriver: true,
  });
};

// === COMPLEX ANIMATIONS ===

/**
 * Staggered animation for list items
 */
export const createStaggeredAnimation = (
  animatedValues: Animated.Value[],
  toValue: number,
  staggerDelay: number = Animations.stagger.normal,
  duration: number = Animations.timing.normal
) => {
  const animations = animatedValues.map((value, index) =>
    Animated.timing(value, {
      toValue,
      duration,
      delay: index * staggerDelay,
      easing: Easing.out(Easing.quad),
      useNativeDriver: true,
    })
  );

  return Animated.parallel(animations);
};

/**
 * Pulse animation for notifications or highlights
 */
export const createPulseAnimation = (
  animatedValue: Animated.Value,
  minValue: number = 1,
  maxValue: number = 1.1,
  duration: number = 1000
) => {
  return Animated.loop(
    Animated.sequence([
      Animated.timing(animatedValue, {
        toValue: maxValue,
        duration: duration / 2,
        easing: Easing.inOut(Easing.quad),
        useNativeDriver: true,
      }),
      Animated.timing(animatedValue, {
        toValue: minValue,
        duration: duration / 2,
        easing: Easing.inOut(Easing.quad),
        useNativeDriver: true,
      }),
    ])
  );
};

/**
 * Shake animation for error states
 */
export const createShakeAnimation = (
  animatedValue: Animated.Value,
  intensity: number = 10,
  duration: number = 500
) => {
  return Animated.sequence([
    Animated.timing(animatedValue, {
      toValue: intensity,
      duration: duration / 8,
      useNativeDriver: true,
    }),
    Animated.timing(animatedValue, {
      toValue: -intensity,
      duration: duration / 8,
      useNativeDriver: true,
    }),
    Animated.timing(animatedValue, {
      toValue: intensity,
      duration: duration / 8,
      useNativeDriver: true,
    }),
    Animated.timing(animatedValue, {
      toValue: -intensity,
      duration: duration / 8,
      useNativeDriver: true,
    }),
    Animated.timing(animatedValue, {
      toValue: 0,
      duration: duration / 2,
      useNativeDriver: true,
    }),
  ]);
};

// === ANIMATION HOOKS ===

/**
 * Button press animation hook
 */
export const useButtonAnimation = () => {
  const scaleValue = new Animated.Value(1);

  const animatePress = () => {
    createScaleAnimation(scaleValue, 0.95).start();
  };

  const animateRelease = () => {
    createScaleAnimation(scaleValue, 1).start();
  };

  return {
    scaleValue,
    animatePress,
    animateRelease,
    animatedStyle: {
      transform: [{ scale: scaleValue }],
    },
  };
};

/**
 * Loading animation hook
 */
export const useLoadingAnimation = () => {
  const rotateValue = new Animated.Value(0);

  const startAnimation = () => {
    return Animated.loop(
      Animated.timing(rotateValue, {
        toValue: 1,
        duration: 1000,
        easing: Easing.linear,
        useNativeDriver: true,
      })
    );
  };

  const stopAnimation = () => {
    rotateValue.stopAnimation();
    rotateValue.setValue(0);
  };

  return {
    rotateValue,
    startAnimation,
    stopAnimation,
    animatedStyle: {
      transform: [
        {
          rotate: rotateValue.interpolate({
            inputRange: [0, 1],
            outputRange: ['0deg', '360deg'],
          }),
        },
      ],
    },
  };
};

/**
 * Slide in animation hook for modals/screens
 */
export const useSlideInAnimation = (initialValue: number = 300) => {
  const slideValue = new Animated.Value(initialValue);
  const opacityValue = new Animated.Value(0);

  const slideIn = () => {
    return Animated.parallel([
      createSlideAnimation(slideValue, 0),
      createFadeAnimation(opacityValue, 1),
    ]);
  };

  const slideOut = () => {
    return Animated.parallel([
      createSlideAnimation(slideValue, initialValue),
      createFadeAnimation(opacityValue, 0),
    ]);
  };

  return {
    slideValue,
    opacityValue,
    slideIn,
    slideOut,
    animatedStyle: {
      opacity: opacityValue,
      transform: [{ translateY: slideValue }],
    },
  };
};

// === PRESET ANIMATIONS ===

export const AnimationPresets = {
  // Button interactions
  buttonPress: {
    scale: 0.95,
    duration: Animations.timing.fast,
  },
  
  // Card interactions
  cardHover: {
    scale: 1.02,
    duration: Animations.timing.normal,
  },
  
  // Loading states
  skeleton: {
    duration: 1500,
    easing: Easing.inOut(Easing.ease),
  },
  
  // Page transitions
  pageSlide: {
    duration: Animations.timing.normal,
    easing: Easing.out(Easing.cubic),
  },
  
  // Toast notifications
  toast: {
    slideDistance: 100,
    duration: Animations.timing.normal,
    autoHideDuration: 3000,
  },
};

export default {
  createScaleAnimation,
  createFadeAnimation,
  createSlideAnimation,
  createSpringAnimation,
  createStaggeredAnimation,
  createPulseAnimation,
  createShakeAnimation,
  useButtonAnimation,
  useLoadingAnimation,
  useSlideInAnimation,
  AnimationPresets,
};
