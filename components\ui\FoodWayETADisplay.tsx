/**
 * FoodWay ETA Display Component
 * Modern ETA display with countdown and prominent icons
 */

import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Animated,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { DesignSystem } from '../../constants/DesignSystem';
import { FoodWayText } from './FoodWayText';

const { Colors, Typography, Spacing, BorderRadius, Shadows } = DesignSystem;

interface FoodWayETADisplayProps {
  estimatedTime: number; // in minutes
  status: 'preparing' | 'cooking' | 'ready' | 'picked_up' | 'on_the_way' | 'delivered';
  deliveryPersonName?: string;
  deliveryPersonPhone?: string;
}

export const FoodWayETADisplay: React.FC<FoodWayETADisplayProps> = ({
  estimatedTime,
  status,
  deliveryPersonName,
  deliveryPersonPhone,
}) => {
  const [timeRemaining, setTimeRemaining] = useState(estimatedTime);
  const [pulseAnim] = useState(new Animated.Value(1));

  useEffect(() => {
    // Start pulse animation for active states
    if (status === 'cooking' || status === 'on_the_way') {
      const pulse = Animated.loop(
        Animated.sequence([
          Animated.timing(pulseAnim, {
            toValue: 1.1,
            duration: 1000,
            useNativeDriver: true,
          }),
          Animated.timing(pulseAnim, {
            toValue: 1,
            duration: 1000,
            useNativeDriver: true,
          }),
        ])
      );
      pulse.start();
      
      return () => pulse.stop();
    }
  }, [status, pulseAnim]);

  useEffect(() => {
    // Countdown timer
    if (timeRemaining > 0 && status !== 'delivered') {
      const timer = setInterval(() => {
        setTimeRemaining(prev => Math.max(0, prev - 1));
      }, 60000); // Update every minute

      return () => clearInterval(timer);
    }
  }, [timeRemaining, status]);

  const getStatusConfig = () => {
    switch (status) {
      case 'preparing':
        return {
          icon: 'receipt-outline' as const,
          title: 'Order Confirmed',
          subtitle: 'Restaurant is preparing your order',
          color: Colors.warning,
          gradientColors: [Colors.warning, Colors.warningDark],
        };
      case 'cooking':
        return {
          icon: 'flame' as const,
          title: 'Cooking in Progress',
          subtitle: 'Your delicious meal is being prepared',
          color: Colors.primary,
          gradientColors: [Colors.primary, Colors.primaryDark],
        };
      case 'ready':
        return {
          icon: 'checkmark-circle' as const,
          title: 'Order Ready',
          subtitle: 'Waiting for pickup by delivery partner',
          color: Colors.success,
          gradientColors: [Colors.success, Colors.successLight],
        };
      case 'picked_up':
        return {
          icon: 'bag-check' as const,
          title: 'Order Picked Up',
          subtitle: 'Delivery partner has your order',
          color: Colors.info,
          gradientColors: [Colors.info, Colors.infoLight],
        };
      case 'on_the_way':
        return {
          icon: 'bicycle' as const,
          title: 'On the Way',
          subtitle: `${deliveryPersonName || 'Delivery partner'} is heading to you`,
          color: Colors.primary,
          gradientColors: [Colors.primary, Colors.primaryDark],
        };
      case 'delivered':
        return {
          icon: 'home' as const,
          title: 'Delivered',
          subtitle: 'Enjoy your meal!',
          color: Colors.success,
          gradientColors: [Colors.success, Colors.successLight],
        };
      default:
        return {
          icon: 'time-outline' as const,
          title: 'Processing',
          subtitle: 'Please wait...',
          color: Colors.textSecondary,
          gradientColors: [Colors.textSecondary, Colors.textLight],
        };
    }
  };

  const statusConfig = getStatusConfig();
  const isActive = status === 'cooking' || status === 'on_the_way';

  const formatTime = (minutes: number) => {
    if (minutes <= 0) return 'Any moment now';
    if (minutes < 60) return `${minutes} min`;
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return `${hours}h ${remainingMinutes}m`;
  };

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={statusConfig.gradientColors}
        style={styles.gradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <View style={styles.content}>
          {/* Icon with Animation */}
          <Animated.View 
            style={[
              styles.iconContainer,
              isActive && { transform: [{ scale: pulseAnim }] }
            ]}
          >
            <Ionicons 
              name={statusConfig.icon} 
              size={40} 
              color={Colors.white} 
            />
          </Animated.View>

          {/* Status Info */}
          <View style={styles.statusInfo}>
            <FoodWayText 
              variant="h3" 
              color="white" 
              style={styles.statusTitle}
            >
              {statusConfig.title}
            </FoodWayText>
            
            <FoodWayText 
              variant="body" 
              color="white" 
              style={styles.statusSubtitle}
            >
              {statusConfig.subtitle}
            </FoodWayText>
          </View>

          {/* ETA Display */}
          {status !== 'delivered' && (
            <View style={styles.etaContainer}>
              <View style={styles.etaContent}>
                <Ionicons 
                  name="time" 
                  size={20} 
                  color={Colors.white} 
                  style={styles.etaIcon}
                />
                <FoodWayText 
                  variant="h2" 
                  color="white" 
                  style={styles.etaTime}
                >
                  {formatTime(timeRemaining)}
                </FoodWayText>
              </View>
              <FoodWayText 
                variant="caption" 
                color="white" 
                style={styles.etaLabel}
              >
                Estimated arrival
              </FoodWayText>
            </View>
          )}
        </View>
      </LinearGradient>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: Spacing.md,
    borderRadius: BorderRadius.xl,
    overflow: 'hidden',
    ...Shadows.md,
  },
  
  gradient: {
    padding: Spacing.xl,
  },
  
  content: {
    alignItems: 'center',
  },
  
  iconContainer: {
    width: 80,
    height: 80,
    borderRadius: BorderRadius.full,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: Spacing.lg,
  },
  
  statusInfo: {
    alignItems: 'center',
    marginBottom: Spacing.lg,
  },
  
  statusTitle: {
    marginBottom: Spacing.xs,
    textAlign: 'center',
  },
  
  statusSubtitle: {
    textAlign: 'center',
    opacity: 0.9,
  },
  
  etaContainer: {
    alignItems: 'center',
  },
  
  etaContent: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.xs,
  },
  
  etaIcon: {
    marginRight: Spacing.sm,
  },
  
  etaTime: {
    fontWeight: Typography.fontWeight.bold,
  },
  
  etaLabel: {
    opacity: 0.8,
    textTransform: 'uppercase',
    letterSpacing: 1,
  },
});

export default FoodWayETADisplay;
