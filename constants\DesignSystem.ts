/**
 * FoodWay Design System
 * Complete design system for premium food delivery experience
 * Designed to surpass competitors like Foodpanda, Zomato, and DoorDash
 */

import { Dimensions, Platform } from 'react-native';
import { Colors } from './Colors';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

// === TYPOGRAPHY SYSTEM ===
export const Typography = {
  // Font Families
  fontFamily: {
    regular: Platform.OS === 'ios' ? 'SF Pro Text' : 'Inter-Regular',
    medium: Platform.OS === 'ios' ? 'SF Pro Text' : 'Inter-Medium',
    semibold: Platform.OS === 'ios' ? 'SF Pro Text' : 'Inter-SemiBold',
    bold: Platform.OS === 'ios' ? 'SF Pro Display' : 'Inter-Bold',
  },
  
  // Font Weights
  fontWeight: {
    light: '300' as const,
    regular: '400' as const,
    medium: '500' as const,
    semibold: '600' as const,
    bold: '700' as const,
    extrabold: '800' as const,
  },
  
  // Responsive Font Sizes
  fontSize: {
    xs: screenWidth < 375 ? 11 : 12,
    sm: screenWidth < 375 ? 13 : 14,
    base: screenWidth < 375 ? 15 : 16,
    lg: screenWidth < 375 ? 17 : 18,
    xl: screenWidth < 375 ? 19 : 20,
    '2xl': screenWidth < 375 ? 22 : 24,
    '3xl': screenWidth < 375 ? 28 : 30,
    '4xl': screenWidth < 375 ? 32 : 36,
  },
  
  // Line Heights
  lineHeight: {
    tight: 1.2,
    normal: 1.4,
    relaxed: 1.6,
    loose: 1.8,
  },
};

// === SPACING SYSTEM ===
export const Spacing = {
  xxs: 2,
  xs: 4,
  sm: 8,
  md: 12,
  lg: 16,
  xl: 20,
  '2xl': 24,
  '3xl': 32,
  '4xl': 40,
  '5xl': 48,
  '6xl': 64,
  
  // Component-specific spacing
  cardPadding: 16,
  sectionSpacing: 24,
  screenPadding: 16,
  buttonPadding: 12,
};

// === BORDER RADIUS SYSTEM ===
export const BorderRadius = {
  none: 0,
  xs: 2,
  sm: 4,
  md: 8,
  lg: 12,
  xl: 16,
  '2xl': 20,
  '3xl': 24,
  full: 9999,
};

// === SHADOW SYSTEM ===
export const Shadows = {
  none: {
    shadowColor: 'transparent',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0,
    shadowRadius: 0,
    elevation: 0,
  },
  xs: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  sm: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  md: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 4,
  },
  lg: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.2,
    shadowRadius: 16,
    elevation: 8,
  },
  xl: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 12 },
    shadowOpacity: 0.25,
    shadowRadius: 24,
    elevation: 12,
  },
};

// === ANIMATION SYSTEM ===
export const Animations = {
  // Timing
  timing: {
    fast: 150,
    normal: 300,
    slow: 500,
    extraSlow: 800,
  },
  
  // Easing
  easing: {
    linear: 'linear',
    easeIn: 'ease-in',
    easeOut: 'ease-out',
    easeInOut: 'ease-in-out',
    bounce: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
  },
  
  // Spring configurations
  spring: {
    tension: 100,
    friction: 8,
    useNativeDriver: true,
  },
  
  // Stagger timing
  stagger: {
    fast: 50,
    normal: 100,
    slow: 150,
  },
};

// === LAYOUT SYSTEM ===
export const Layout = {
  // Screen breakpoints
  breakpoints: {
    small: 375,
    medium: 414,
    large: 768,
  },
  
  // Container widths
  container: {
    small: screenWidth - 32,
    medium: Math.min(screenWidth - 32, 600),
    large: Math.min(screenWidth - 32, 800),
  },
  
  // Common dimensions
  dimensions: {
    headerHeight: 60,
    tabBarHeight: 80,
    buttonHeight: 48,
    inputHeight: 48,
    cardMinHeight: 120,
    touchTarget: 44,
  },
  
  // Grid system
  grid: {
    columns: 12,
    gutter: 16,
  },
};

// === COMPONENT VARIANTS ===
export const ComponentVariants = {
  button: {
    primary: {
      backgroundColor: Colors.primary,
      borderColor: Colors.primary,
      borderWidth: 0,
    },
    secondary: {
      backgroundColor: Colors.success,
      borderColor: Colors.success,
      borderWidth: 0,
    },
    outline: {
      backgroundColor: 'transparent',
      borderColor: Colors.primary,
      borderWidth: 1,
    },
    ghost: {
      backgroundColor: 'transparent',
      borderColor: 'transparent',
      borderWidth: 0,
    },
    danger: {
      backgroundColor: Colors.error,
      borderColor: Colors.error,
      borderWidth: 0,
    },
  },
  
  card: {
    default: {
      backgroundColor: Colors.surface,
      borderRadius: BorderRadius.lg,
      ...Shadows.sm,
    },
    elevated: {
      backgroundColor: Colors.surfaceElevated,
      borderRadius: BorderRadius.lg,
      ...Shadows.md,
    },
    outlined: {
      backgroundColor: Colors.surface,
      borderRadius: BorderRadius.lg,
      borderWidth: 1,
      borderColor: Colors.border,
    },
  },
  
  input: {
    default: {
      backgroundColor: Colors.surface,
      borderRadius: BorderRadius.md,
      borderWidth: 1,
      borderColor: Colors.border,
      height: Layout.dimensions.inputHeight,
    },
    focused: {
      borderColor: Colors.primary,
      borderWidth: 2,
    },
    error: {
      borderColor: Colors.error,
      borderWidth: 1,
    },
  },
};

// === UTILITY FUNCTIONS ===
export const Utils = {
  // Responsive value based on screen width
  responsive: (small: any, medium?: any, large?: any) => {
    if (screenWidth < Layout.breakpoints.small) return small;
    if (screenWidth < Layout.breakpoints.medium) return medium || small;
    return large || medium || small;
  },
  
  // Scale value based on screen width
  scale: (size: number) => {
    const scale = screenWidth / 375; // Base width
    return Math.round(size * scale);
  },
  
  // Get appropriate shadow for elevation
  getShadow: (elevation: keyof typeof Shadows) => {
    return Shadows[elevation] || Shadows.sm;
  },
  
  // Platform-specific values
  platform: <T>(ios: T, android: T): T => {
    return Platform.OS === 'ios' ? ios : android;
  },
  
  // Safe area calculations
  safeArea: {
    top: Platform.OS === 'ios' ? 44 : 0,
    bottom: Platform.OS === 'ios' ? 34 : 0,
  },
};

// Export the complete design system
export const DesignSystem = {
  Colors,
  Typography,
  Spacing,
  BorderRadius,
  Shadows,
  Animations,
  Layout,
  ComponentVariants,
  Utils,
};

export default DesignSystem;
