/**
 * FoodWay Accessibility Utilities
 * WCAG 2.1 AA compliance utilities with prominent icon support
 */

import { AccessibilityInfo, Platform } from 'react-native';
import { Colors } from '../constants/Colors';

// === COLOR CONTRAST UTILITIES ===

/**
 * Calculate relative luminance of a color (WCAG formula)
 */
const getRelativeLuminance = (color: string): number => {
  // Convert hex to RGB
  const hex = color.replace('#', '');
  const r = parseInt(hex.substr(0, 2), 16) / 255;
  const g = parseInt(hex.substr(2, 2), 16) / 255;
  const b = parseInt(hex.substr(4, 2), 16) / 255;

  // Apply gamma correction
  const sRGB = [r, g, b].map(c => {
    return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
  });

  // Calculate relative luminance
  return 0.2126 * sRGB[0] + 0.7152 * sRGB[1] + 0.0722 * sRGB[2];
};

/**
 * Calculate contrast ratio between two colors
 */
export const getContrastRatio = (color1: string, color2: string): number => {
  const l1 = getRelativeLuminance(color1);
  const l2 = getRelativeLuminance(color2);
  const lighter = Math.max(l1, l2);
  const darker = Math.min(l1, l2);
  
  return (lighter + 0.05) / (darker + 0.05);
};

/**
 * Check if color combination meets WCAG AA standards
 */
export const meetsWCAGAA = (
  foreground: string, 
  background: string, 
  isLargeText: boolean = false
): boolean => {
  const ratio = getContrastRatio(foreground, background);
  return isLargeText ? ratio >= 3 : ratio >= 4.5;
};

/**
 * Check if color combination meets WCAG AAA standards
 */
export const meetsWCAGAAA = (
  foreground: string, 
  background: string, 
  isLargeText: boolean = false
): boolean => {
  const ratio = getContrastRatio(foreground, background);
  return isLargeText ? ratio >= 4.5 : ratio >= 7;
};

/**
 * Get accessible text color for a given background
 */
export const getAccessibleTextColor = (backgroundColor: string): string => {
  const whiteRatio = getContrastRatio('#FFFFFF', backgroundColor);
  const blackRatio = getContrastRatio('#000000', backgroundColor);
  
  return whiteRatio > blackRatio ? '#FFFFFF' : '#000000';
};

// === ACCESSIBILITY PROPS GENERATORS ===

/**
 * Generate accessibility props for buttons with icons
 */
export const createButtonAccessibilityProps = (
  label: string,
  hint?: string,
  disabled: boolean = false,
  iconName?: string,
  iconDescription?: string
) => {
  let accessibilityLabel = label;
  
  if (iconName && iconDescription) {
    accessibilityLabel = `${iconDescription}, ${label}`;
  } else if (iconName) {
    // Provide default descriptions for common icons
    const iconDescriptions: Record<string, string> = {
      'heart': 'favorite',
      'heart-outline': 'add to favorites',
      'star': 'rating',
      'add': 'add',
      'remove': 'remove',
      'cart': 'shopping cart',
      'search': 'search',
      'filter': 'filter',
      'call': 'call',
      'message': 'message',
      'share': 'share',
      'location': 'location',
      'time': 'time',
      'restaurant': 'restaurant',
      'bicycle': 'delivery',
      'home': 'home',
    };
    
    const defaultDescription = iconDescriptions[iconName];
    if (defaultDescription) {
      accessibilityLabel = `${defaultDescription}, ${label}`;
    }
  }

  return {
    accessible: true,
    accessibilityRole: 'button' as const,
    accessibilityLabel,
    accessibilityHint: hint,
    accessibilityState: { disabled },
    ...(Platform.OS === 'ios' && {
      accessibilityTraits: disabled ? ['button', 'disabled'] : ['button'],
    }),
  };
};

/**
 * Generate accessibility props for text inputs with icons
 */
export const createInputAccessibilityProps = (
  label: string,
  value?: string,
  error?: string,
  required: boolean = false,
  iconName?: string,
  iconDescription?: string
) => {
  let accessibilityLabel = label;
  
  if (iconName && iconDescription) {
    accessibilityLabel = `${iconDescription}, ${label}`;
  }

  return {
    accessible: true,
    accessibilityRole: 'text' as const,
    accessibilityLabel,
    accessibilityValue: value ? { text: value } : undefined,
    accessibilityState: { 
      invalid: !!error,
      required,
    },
    accessibilityHint: error || (required ? 'Required field' : undefined),
  };
};

/**
 * Generate accessibility props for images with prominent icons
 */
export const createImageAccessibilityProps = (
  description: string,
  decorative: boolean = false
) => {
  if (decorative) {
    return {
      accessible: false,
      accessibilityElementsHidden: true,
      importantForAccessibility: 'no-hide-descendants' as const,
    };
  }

  return {
    accessible: true,
    accessibilityRole: 'image' as const,
    accessibilityLabel: description,
  };
};

/**
 * Generate accessibility props for cards/touchable items
 */
export const createCardAccessibilityProps = (
  title: string,
  description?: string,
  additionalInfo?: string[]
) => {
  let accessibilityLabel = title;
  
  if (description) {
    accessibilityLabel += `, ${description}`;
  }
  
  if (additionalInfo && additionalInfo.length > 0) {
    accessibilityLabel += `, ${additionalInfo.join(', ')}`;
  }

  return {
    accessible: true,
    accessibilityRole: 'button' as const,
    accessibilityLabel,
    accessibilityHint: 'Double tap to open',
  };
};

// === SCREEN READER UTILITIES ===

/**
 * Check if screen reader is enabled
 */
export const isScreenReaderEnabled = async (): Promise<boolean> => {
  try {
    return await AccessibilityInfo.isScreenReaderEnabled();
  } catch (error) {
    console.warn('Failed to check screen reader status:', error);
    return false;
  }
};

/**
 * Announce message to screen reader
 */
export const announceToScreenReader = (message: string) => {
  if (Platform.OS === 'ios') {
    AccessibilityInfo.announceForAccessibility(message);
  } else {
    // Android equivalent
    AccessibilityInfo.setAccessibilityFocus(message as any);
  }
};

// === FOCUS MANAGEMENT ===

/**
 * Set accessibility focus to an element
 */
export const setAccessibilityFocus = (reactTag: number) => {
  if (Platform.OS === 'ios') {
    AccessibilityInfo.setAccessibilityFocus(reactTag);
  }
};

// === VALIDATION UTILITIES ===

/**
 * Validate color combinations for accessibility
 */
export const validateColorAccessibility = () => {
  const validations = [
    {
      name: 'Primary text on white background',
      passes: meetsWCAGAA(Colors.textPrimary, Colors.white),
      ratio: getContrastRatio(Colors.textPrimary, Colors.white),
    },
    {
      name: 'Secondary text on white background',
      passes: meetsWCAGAA(Colors.textSecondary, Colors.white),
      ratio: getContrastRatio(Colors.textSecondary, Colors.white),
    },
    {
      name: 'Primary button text',
      passes: meetsWCAGAA(Colors.white, Colors.primary),
      ratio: getContrastRatio(Colors.white, Colors.primary),
    },
    {
      name: 'Success text',
      passes: meetsWCAGAA(Colors.success, Colors.white),
      ratio: getContrastRatio(Colors.success, Colors.white),
    },
    {
      name: 'Error text',
      passes: meetsWCAGAA(Colors.error, Colors.white),
      ratio: getContrastRatio(Colors.error, Colors.white),
    },
  ];

  const failedValidations = validations.filter(v => !v.passes);
  
  if (failedValidations.length > 0) {
    console.warn('Accessibility color validation failures:', failedValidations);
  }

  return {
    allPassed: failedValidations.length === 0,
    validations,
    failedValidations,
  };
};

// === TOUCH TARGET UTILITIES ===

/**
 * Ensure minimum touch target size (44x44 points)
 */
export const ensureMinimumTouchTarget = (width: number, height: number) => {
  const minSize = 44;
  return {
    width: Math.max(width, minSize),
    height: Math.max(height, minSize),
    paddingHorizontal: Math.max(0, (minSize - width) / 2),
    paddingVertical: Math.max(0, (minSize - height) / 2),
  };
};

// === SEMANTIC MARKUP UTILITIES ===

/**
 * Create semantic heading props
 */
export const createHeadingProps = (level: 1 | 2 | 3 | 4 | 5 | 6) => ({
  accessible: true,
  accessibilityRole: 'header' as const,
  accessibilityLevel: level,
});

/**
 * Create list accessibility props
 */
export const createListProps = (itemCount: number) => ({
  accessible: true,
  accessibilityRole: 'list' as const,
  accessibilityLabel: `List with ${itemCount} items`,
});

/**
 * Create list item accessibility props
 */
export const createListItemProps = (index: number, total: number) => ({
  accessible: true,
  accessibilityRole: 'listitem' as const,
  accessibilityLabel: `Item ${index + 1} of ${total}`,
});

export default {
  getContrastRatio,
  meetsWCAGAA,
  meetsWCAGAAA,
  getAccessibleTextColor,
  createButtonAccessibilityProps,
  createInputAccessibilityProps,
  createImageAccessibilityProps,
  createCardAccessibilityProps,
  isScreenReaderEnabled,
  announceToScreenReader,
  setAccessibilityFocus,
  validateColorAccessibility,
  ensureMinimumTouchTarget,
  createHeadingProps,
  createListProps,
  createListItemProps,
};
