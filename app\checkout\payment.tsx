/**
 * FoodWay Payment Screen
 * Modern payment interface with prominent icons and multiple payment methods
 */

import React, { useState, useRef } from 'react';
import {
  View,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Animated,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { 
  FoodWayText,
  FoodWayCard,
  FoodWayButton,
  FoodWayInput,
  FoodWayStickyHeader,
  Colors,
  DesignSystem 
} from '../../components/ui';

const { Spacing, BorderRadius, Typography, Shadows } = DesignSystem;

// Payment methods with prominent icons
const paymentMethods = [
  {
    id: 'card',
    name: 'Credit/Debit Card',
    description: 'Visa, Mastercard, American Express',
    icon: 'card' as const,
    color: Colors.primary,
    popular: true,
  },
  {
    id: 'paypal',
    name: 'PayPal',
    description: 'Pay with your PayPal account',
    icon: 'logo-paypal' as const,
    color: '#0070BA',
    popular: false,
  },
  {
    id: 'apple-pay',
    name: 'Apple Pay',
    description: 'Touch ID or Face ID',
    icon: 'logo-apple' as const,
    color: '#000000',
    popular: true,
  },
  {
    id: 'google-pay',
    name: 'Google Pay',
    description: 'Pay with Google',
    icon: 'logo-google' as const,
    color: '#4285F4',
    popular: false,
  },
  {
    id: 'cash',
    name: 'Cash on Delivery',
    description: 'Pay when your order arrives',
    icon: 'cash' as const,
    color: Colors.success,
    popular: false,
  },
];

export default function PaymentScreen() {
  const [selectedMethod, setSelectedMethod] = useState('card');
  const [cardNumber, setCardNumber] = useState('');
  const [expiryDate, setExpiryDate] = useState('');
  const [cvv, setCvv] = useState('');
  const [cardholderName, setCardholderName] = useState('');
  const [saveCard, setSaveCard] = useState(false);
  const [loading, setLoading] = useState(false);
  
  const fadeAnim = useRef(new Animated.Value(0)).current;

  React.useEffect(() => {
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 500,
      useNativeDriver: true,
    }).start();
  }, []);

  const handlePaymentMethodSelect = (methodId: string) => {
    setSelectedMethod(methodId);
  };

  const handlePayment = async () => {
    setLoading(true);
    
    // Simulate payment processing
    setTimeout(() => {
      setLoading(false);
      Alert.alert(
        'Payment Successful!',
        'Your order has been placed successfully.',
        [
          {
            text: 'Track Order',
            onPress: () => router.push('/order/12345'),
          },
        ]
      );
    }, 2000);
  };

  const renderPaymentMethod = (method: typeof paymentMethods[0]) => (
    <TouchableOpacity
      key={method.id}
      onPress={() => handlePaymentMethodSelect(method.id)}
      activeOpacity={0.8}
    >
      <FoodWayCard 
        variant={selectedMethod === method.id ? "elevated" : "default"}
        style={[
          styles.paymentMethodCard,
          selectedMethod === method.id && styles.selectedPaymentMethod,
        ]}
      >
        <View style={styles.paymentMethodContent}>
          <View style={[styles.paymentMethodIcon, { backgroundColor: `${method.color}20` }]}>
            <Ionicons name={method.icon} size={24} color={method.color} />
          </View>
          
          <View style={styles.paymentMethodInfo}>
            <View style={styles.paymentMethodHeader}>
              <FoodWayText variant="h4" style={styles.paymentMethodName}>
                {method.name}
              </FoodWayText>
              {method.popular && (
                <View style={styles.popularBadge}>
                  <FoodWayText variant="caption" color="white">
                    Popular
                  </FoodWayText>
                </View>
              )}
            </View>
            <FoodWayText variant="bodySmall" color="textSecondary">
              {method.description}
            </FoodWayText>
          </View>
          
          <View style={styles.radioButton}>
            {selectedMethod === method.id && (
              <View style={styles.radioButtonSelected}>
                <Ionicons name="checkmark" size={12} color={Colors.white} />
              </View>
            )}
          </View>
        </View>
      </FoodWayCard>
    </TouchableOpacity>
  );

  const renderCardForm = () => {
    if (selectedMethod !== 'card') return null;

    return (
      <FoodWayCard variant="elevated" style={styles.cardFormContainer}>
        <FoodWayText variant="h3" style={styles.cardFormTitle}>
          Card Details
        </FoodWayText>
        
        <FoodWayInput
          label="Card Number"
          placeholder="1234 5678 9012 3456"
          value={cardNumber}
          onChangeText={setCardNumber}
          keyboardType="numeric"
          leftIcon={<Ionicons name="card" size={20} color={Colors.textSecondary} />}
          style={styles.cardInput}
        />
        
        <View style={styles.cardRow}>
          <FoodWayInput
            label="Expiry Date"
            placeholder="MM/YY"
            value={expiryDate}
            onChangeText={setExpiryDate}
            keyboardType="numeric"
            style={[styles.cardInput, styles.cardInputHalf]}
          />
          
          <FoodWayInput
            label="CVV"
            placeholder="123"
            value={cvv}
            onChangeText={setCvv}
            keyboardType="numeric"
            secureTextEntry
            style={[styles.cardInput, styles.cardInputHalf]}
          />
        </View>
        
        <FoodWayInput
          label="Cardholder Name"
          placeholder="John Doe"
          value={cardholderName}
          onChangeText={setCardholderName}
          leftIcon={<Ionicons name="person" size={20} color={Colors.textSecondary} />}
          style={styles.cardInput}
        />
        
        <TouchableOpacity
          style={styles.saveCardOption}
          onPress={() => setSaveCard(!saveCard)}
          activeOpacity={0.8}
        >
          <View style={[styles.checkbox, saveCard && styles.checkboxSelected]}>
            {saveCard && <Ionicons name="checkmark" size={12} color={Colors.white} />}
          </View>
          <FoodWayText variant="body" style={styles.saveCardText}>
            Save card for future payments
          </FoodWayText>
        </TouchableOpacity>
      </FoodWayCard>
    );
  };

  return (
    <View style={styles.container}>
      <FoodWayStickyHeader
        title="Payment"
        subtitle="Choose your payment method"
        onBackPress={() => router.back()}
      />

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <Animated.View style={[styles.content, { opacity: fadeAnim }]}>
          {/* Payment Methods */}
          <View style={styles.paymentMethodsSection}>
            <FoodWayText variant="h3" style={styles.sectionTitle}>
              Payment Methods
            </FoodWayText>
            {paymentMethods.map(renderPaymentMethod)}
          </View>

          {/* Card Form */}
          {renderCardForm()}

          {/* Order Summary */}
          <FoodWayCard variant="elevated" style={styles.orderSummary}>
            <FoodWayText variant="h3" style={styles.summaryTitle}>
              Order Summary
            </FoodWayText>
            
            <View style={styles.summaryRow}>
              <FoodWayText variant="body">Subtotal</FoodWayText>
              <FoodWayText variant="body">$32.99</FoodWayText>
            </View>
            
            <View style={styles.summaryRow}>
              <FoodWayText variant="body">Delivery Fee</FoodWayText>
              <FoodWayText variant="body">$2.99</FoodWayText>
            </View>
            
            <View style={styles.summaryRow}>
              <FoodWayText variant="body">Tax</FoodWayText>
              <FoodWayText variant="body">$3.15</FoodWayText>
            </View>
            
            <View style={[styles.summaryRow, styles.totalRow]}>
              <FoodWayText variant="h4" color="primary">Total</FoodWayText>
              <FoodWayText variant="h4" color="primary">$39.13</FoodWayText>
            </View>
          </FoodWayCard>

          {/* Payment Button */}
          <FoodWayButton
            title={loading ? "Processing..." : "Complete Payment • $39.13"}
            variant="primary"
            size="large"
            fullWidth
            loading={loading}
            leftIcon={!loading ? <Ionicons name="card" size={20} color={Colors.white} /> : undefined}
            onPress={handlePayment}
            style={styles.paymentButton}
          />

          <View style={styles.bottomSpacing} />
        </Animated.View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },

  scrollView: {
    flex: 1,
  },

  content: {
    paddingTop: 100, // Account for sticky header
  },

  paymentMethodsSection: {
    marginHorizontal: Spacing.lg,
    marginBottom: Spacing.lg,
  },

  sectionTitle: {
    marginBottom: Spacing.md,
  },

  paymentMethodCard: {
    marginBottom: Spacing.sm,
  },

  selectedPaymentMethod: {
    borderColor: Colors.primary,
    borderWidth: 2,
  },

  paymentMethodContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },

  paymentMethodIcon: {
    width: 48,
    height: 48,
    borderRadius: BorderRadius.full,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: Spacing.md,
  },

  paymentMethodInfo: {
    flex: 1,
  },

  paymentMethodHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 2,
  },

  paymentMethodName: {
    flex: 1,
  },

  popularBadge: {
    backgroundColor: Colors.primary,
    paddingHorizontal: Spacing.xs,
    paddingVertical: 2,
    borderRadius: BorderRadius.sm,
  },

  radioButton: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: Colors.border,
    alignItems: 'center',
    justifyContent: 'center',
  },

  radioButtonSelected: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: Colors.primary,
    alignItems: 'center',
    justifyContent: 'center',
  },

  cardFormContainer: {
    marginHorizontal: Spacing.lg,
    marginBottom: Spacing.lg,
  },

  cardFormTitle: {
    marginBottom: Spacing.md,
  },

  cardInput: {
    marginBottom: Spacing.md,
  },

  cardRow: {
    flexDirection: 'row',
    gap: Spacing.md,
  },

  cardInputHalf: {
    flex: 1,
  },

  saveCardOption: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: Spacing.sm,
  },

  checkbox: {
    width: 20,
    height: 20,
    borderRadius: 4,
    borderWidth: 2,
    borderColor: Colors.border,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: Spacing.sm,
  },

  checkboxSelected: {
    backgroundColor: Colors.primary,
    borderColor: Colors.primary,
  },

  saveCardText: {
    flex: 1,
  },

  orderSummary: {
    marginHorizontal: Spacing.lg,
    marginBottom: Spacing.lg,
  },

  summaryTitle: {
    marginBottom: Spacing.md,
  },

  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.sm,
  },

  totalRow: {
    borderTopWidth: 1,
    borderTopColor: Colors.border,
    paddingTop: Spacing.sm,
    marginTop: Spacing.sm,
  },

  paymentButton: {
    marginHorizontal: Spacing.lg,
    marginBottom: Spacing.lg,
  },

  bottomSpacing: {
    height: Spacing['6xl'],
  },
});
