/**
 * FoodWay Contact Actions Component
 * Modern contact buttons with prominent icons and actions
 */

import React from 'react';
import {
  View,
  TouchableOpacity,
  StyleSheet,
  Linking,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { DesignSystem } from '../../constants/DesignSystem';
import { FoodWayText } from './FoodWayText';
import { FoodWayCard } from './FoodWayCard';

const { Colors, Typography, Spacing, BorderRadius, Shadows } = DesignSystem;

interface ContactInfo {
  name: string;
  phone: string;
  role: 'restaurant' | 'delivery';
}

interface FoodWayContactActionsProps {
  restaurant?: ContactInfo;
  deliveryPerson?: ContactInfo;
  orderId: string;
}

export const FoodWayContactActions: React.FC<FoodWayContactActionsProps> = ({
  restaurant,
  deliveryPerson,
  orderId,
}) => {
  const handleCall = (phone: string, name: string) => {
    const phoneUrl = `tel:${phone}`;
    Linking.canOpenURL(phoneUrl)
      .then((supported) => {
        if (supported) {
          Linking.openURL(phoneUrl);
        } else {
          Alert.alert('Error', 'Phone calls are not supported on this device');
        }
      })
      .catch(() => {
        Alert.alert('Error', 'Unable to make phone call');
      });
  };

  const handleMessage = (phone: string, name: string) => {
    const smsUrl = `sms:${phone}?body=Hi ${name}, regarding my order ${orderId}...`;
    Linking.canOpenURL(smsUrl)
      .then((supported) => {
        if (supported) {
          Linking.openURL(smsUrl);
        } else {
          Alert.alert('Error', 'SMS is not supported on this device');
        }
      })
      .catch(() => {
        Alert.alert('Error', 'Unable to send message');
      });
  };

  const renderContactCard = (contact: ContactInfo) => {
    const isRestaurant = contact.role === 'restaurant';
    const iconName = isRestaurant ? 'restaurant' : 'bicycle';
    const iconColor = isRestaurant ? Colors.warning : Colors.primary;
    const iconBackground = isRestaurant ? Colors.warningAlpha : Colors.primaryAlpha;

    return (
      <FoodWayCard key={contact.role} variant="elevated" style={styles.contactCard}>
        <View style={styles.contactHeader}>
          <View style={[styles.contactIcon, { backgroundColor: iconBackground }]}>
            <Ionicons name={iconName} size={24} color={iconColor} />
          </View>
          <View style={styles.contactInfo}>
            <FoodWayText variant="h4" style={styles.contactName}>
              {contact.name}
            </FoodWayText>
            <FoodWayText variant="caption" color="textSecondary">
              {isRestaurant ? 'Restaurant' : 'Delivery Partner'}
            </FoodWayText>
          </View>
        </View>

        <View style={styles.actionButtons}>
          {/* Call Button */}
          <TouchableOpacity
            style={[styles.actionButton, styles.callButton]}
            onPress={() => handleCall(contact.phone, contact.name)}
            activeOpacity={0.8}
          >
            <Ionicons name="call" size={20} color={Colors.white} />
            <FoodWayText variant="button" color="white" style={styles.buttonText}>
              Call
            </FoodWayText>
          </TouchableOpacity>

          {/* Message Button */}
          <TouchableOpacity
            style={[styles.actionButton, styles.messageButton]}
            onPress={() => handleMessage(contact.phone, contact.name)}
            activeOpacity={0.8}
          >
            <Ionicons name="chatbubble" size={20} color={Colors.primary} />
            <FoodWayText variant="button" color="primary" style={styles.buttonText}>
              Message
            </FoodWayText>
          </TouchableOpacity>
        </View>
      </FoodWayCard>
    );
  };

  const renderEmergencyActions = () => (
    <FoodWayCard variant="outlined" style={styles.emergencyCard}>
      <View style={styles.emergencyHeader}>
        <View style={[styles.contactIcon, { backgroundColor: Colors.errorAlpha }]}>
          <Ionicons name="help-circle" size={24} color={Colors.error} />
        </View>
        <View style={styles.contactInfo}>
          <FoodWayText variant="h4" style={styles.contactName}>
            Need Help?
          </FoodWayText>
          <FoodWayText variant="caption" color="textSecondary">
            Customer Support
          </FoodWayText>
        </View>
      </View>

      <View style={styles.emergencyActions}>
        <TouchableOpacity
          style={[styles.emergencyButton, styles.supportButton]}
          onPress={() => handleCall('+1-800-FOODWAY', 'Customer Support')}
          activeOpacity={0.8}
        >
          <Ionicons name="headset" size={18} color={Colors.white} />
          <FoodWayText variant="bodySmall" color="white" style={styles.emergencyButtonText}>
            Call Support
          </FoodWayText>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.emergencyButton, styles.chatButton]}
          onPress={() => {
            // Navigate to chat support
            Alert.alert('Chat Support', 'Opening chat support...');
          }}
          activeOpacity={0.8}
        >
          <Ionicons name="chatbubbles" size={18} color={Colors.info} />
          <FoodWayText variant="bodySmall" color="info" style={styles.emergencyButtonText}>
            Live Chat
          </FoodWayText>
        </TouchableOpacity>
      </View>
    </FoodWayCard>
  );

  return (
    <View style={styles.container}>
      <FoodWayText variant="h3" style={styles.title}>
        Contact & Support
      </FoodWayText>

      {restaurant && renderContactCard(restaurant)}
      {deliveryPerson && renderContactCard(deliveryPerson)}
      {renderEmergencyActions()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: Spacing.lg,
  },

  title: {
    marginBottom: Spacing.lg,
    paddingHorizontal: Spacing.lg,
  },

  contactCard: {
    marginHorizontal: Spacing.lg,
    marginBottom: Spacing.md,
  },

  contactHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.md,
  },

  contactIcon: {
    width: 48,
    height: 48,
    borderRadius: BorderRadius.full,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: Spacing.md,
  },

  contactInfo: {
    flex: 1,
  },

  contactName: {
    marginBottom: 2,
  },

  actionButtons: {
    flexDirection: 'row',
    gap: Spacing.sm,
  },

  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: Spacing.md,
    borderRadius: BorderRadius.md,
    gap: Spacing.xs,
  },

  callButton: {
    backgroundColor: Colors.success,
  },

  messageButton: {
    backgroundColor: Colors.white,
    borderWidth: 1,
    borderColor: Colors.primary,
  },

  buttonText: {
    fontSize: Typography.fontSize.sm,
    fontWeight: Typography.fontWeight.semibold,
  },

  emergencyCard: {
    marginHorizontal: Spacing.lg,
    borderColor: Colors.errorAlpha,
  },

  emergencyHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.md,
  },

  emergencyActions: {
    flexDirection: 'row',
    gap: Spacing.sm,
  },

  emergencyButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: Spacing.sm,
    borderRadius: BorderRadius.sm,
    gap: Spacing.xs,
  },

  supportButton: {
    backgroundColor: Colors.error,
  },

  chatButton: {
    backgroundColor: Colors.white,
    borderWidth: 1,
    borderColor: Colors.info,
  },

  emergencyButtonText: {
    fontSize: Typography.fontSize.xs,
    fontWeight: Typography.fontWeight.medium,
  },
});

export default FoodWayContactActions;
