/**
 * FoodWay Settings Screen
 * Modern settings screen with prominent icons and organized sections
 */

import React, { useState, useRef } from 'react';
import {
  View,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Animated,
  Switch,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { 
  FoodWayText,
  FoodWayCard,
  FoodWayButton,
  FoodWayStickyHeader,
  Colors,
  DesignSystem 
} from '../components/ui';

const { Spacing, BorderRadius, Typography, Shadows } = DesignSystem;

// Settings sections with prominent icons
const settingsSections = [
  {
    id: 'account',
    title: 'Account',
    icon: 'person' as const,
    color: Colors.primary,
    items: [
      {
        id: 'profile',
        title: 'Profile Information',
        subtitle: 'Update your personal details',
        icon: 'person-circle' as const,
        type: 'navigation' as const,
        route: '/profile/edit',
      },
      {
        id: 'addresses',
        title: 'Delivery Addresses',
        subtitle: 'Manage your saved addresses',
        icon: 'location' as const,
        type: 'navigation' as const,
        route: '/profile/addresses',
      },
      {
        id: 'payment',
        title: 'Payment Methods',
        subtitle: 'Manage cards and payment options',
        icon: 'card' as const,
        type: 'navigation' as const,
        route: '/profile/payment',
      },
    ],
  },
  {
    id: 'notifications',
    title: 'Notifications',
    icon: 'notifications' as const,
    color: Colors.info,
    items: [
      {
        id: 'push-notifications',
        title: 'Push Notifications',
        subtitle: 'Order updates and promotions',
        icon: 'notifications' as const,
        type: 'toggle' as const,
        value: true,
      },
      {
        id: 'email-notifications',
        title: 'Email Notifications',
        subtitle: 'Receipts and marketing emails',
        icon: 'mail' as const,
        type: 'toggle' as const,
        value: false,
      },
      {
        id: 'sms-notifications',
        title: 'SMS Notifications',
        subtitle: 'Order status via text message',
        icon: 'chatbubble' as const,
        type: 'toggle' as const,
        value: true,
      },
    ],
  },
  {
    id: 'preferences',
    title: 'Preferences',
    icon: 'settings' as const,
    color: Colors.success,
    items: [
      {
        id: 'dark-mode',
        title: 'Dark Mode',
        subtitle: 'Switch to dark theme',
        icon: 'moon' as const,
        type: 'toggle' as const,
        value: false,
      },
      {
        id: 'location-services',
        title: 'Location Services',
        subtitle: 'Auto-detect your location',
        icon: 'navigate' as const,
        type: 'toggle' as const,
        value: true,
      },
      {
        id: 'dietary-preferences',
        title: 'Dietary Preferences',
        subtitle: 'Set your food preferences',
        icon: 'leaf' as const,
        type: 'navigation' as const,
        route: '/settings/dietary',
      },
    ],
  },
  {
    id: 'support',
    title: 'Support & Legal',
    icon: 'help-circle' as const,
    color: Colors.warning,
    items: [
      {
        id: 'help-center',
        title: 'Help Center',
        subtitle: 'FAQs and support articles',
        icon: 'help-circle' as const,
        type: 'navigation' as const,
        route: '/help',
      },
      {
        id: 'contact-support',
        title: 'Contact Support',
        subtitle: 'Get help from our team',
        icon: 'headset' as const,
        type: 'navigation' as const,
        route: '/support',
      },
      {
        id: 'privacy-policy',
        title: 'Privacy Policy',
        subtitle: 'How we protect your data',
        icon: 'shield-checkmark' as const,
        type: 'navigation' as const,
        route: '/privacy',
      },
      {
        id: 'terms-of-service',
        title: 'Terms of Service',
        subtitle: 'Our terms and conditions',
        icon: 'document-text' as const,
        type: 'navigation' as const,
        route: '/terms',
      },
    ],
  },
];

export default function SettingsScreen() {
  const [settings, setSettings] = useState<Record<string, boolean>>({
    'push-notifications': true,
    'email-notifications': false,
    'sms-notifications': true,
    'dark-mode': false,
    'location-services': true,
  });
  
  const fadeAnim = useRef(new Animated.Value(0)).current;

  React.useEffect(() => {
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 500,
      useNativeDriver: true,
    }).start();
  }, []);

  const handleToggle = (settingId: string, value: boolean) => {
    setSettings(prev => ({ ...prev, [settingId]: value }));
    
    // Handle specific settings
    switch (settingId) {
      case 'dark-mode':
        Alert.alert('Dark Mode', value ? 'Dark mode enabled' : 'Dark mode disabled');
        break;
      case 'location-services':
        if (value) {
          Alert.alert('Location Services', 'Location access enabled');
        }
        break;
    }
  };

  const handleNavigation = (route: string) => {
    router.push(route as any);
  };

  const handleLogout = () => {
    Alert.alert(
      'Sign Out',
      'Are you sure you want to sign out?',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Sign Out', 
          style: 'destructive',
          onPress: () => {
            // Handle logout
            router.replace('/(auth)/login');
          }
        },
      ]
    );
  };

  const renderSettingItem = (item: any) => {
    const isToggle = item.type === 'toggle';
    
    return (
      <TouchableOpacity
        key={item.id}
        style={styles.settingItem}
        onPress={() => isToggle ? null : handleNavigation(item.route)}
        activeOpacity={isToggle ? 1 : 0.8}
        disabled={isToggle}
      >
        <View style={styles.settingItemContent}>
          <View style={[styles.settingItemIcon, { backgroundColor: `${Colors.primary}20` }]}>
            <Ionicons name={item.icon} size={20} color={Colors.primary} />
          </View>
          
          <View style={styles.settingItemInfo}>
            <FoodWayText variant="body" style={styles.settingItemTitle}>
              {item.title}
            </FoodWayText>
            <FoodWayText variant="bodySmall" color="textSecondary">
              {item.subtitle}
            </FoodWayText>
          </View>
          
          {isToggle ? (
            <Switch
              value={settings[item.id] || false}
              onValueChange={(value) => handleToggle(item.id, value)}
              trackColor={{ false: Colors.border, true: Colors.primaryAlpha }}
              thumbColor={settings[item.id] ? Colors.primary : Colors.textLight}
            />
          ) : (
            <Ionicons name="chevron-forward" size={20} color={Colors.textSecondary} />
          )}
        </View>
      </TouchableOpacity>
    );
  };

  const renderSection = (section: typeof settingsSections[0]) => (
    <FoodWayCard key={section.id} variant="elevated" style={styles.sectionCard}>
      <View style={styles.sectionHeader}>
        <View style={[styles.sectionIcon, { backgroundColor: `${section.color}20` }]}>
          <Ionicons name={section.icon} size={24} color={section.color} />
        </View>
        <FoodWayText variant="h4" style={styles.sectionTitle}>
          {section.title}
        </FoodWayText>
      </View>
      
      <View style={styles.sectionItems}>
        {section.items.map(renderSettingItem)}
      </View>
    </FoodWayCard>
  );

  return (
    <View style={styles.container}>
      <FoodWayStickyHeader
        title="Settings"
        subtitle="Manage your preferences"
        onBackPress={() => router.back()}
      />

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <Animated.View style={[styles.content, { opacity: fadeAnim }]}>
          {/* Settings Sections */}
          {settingsSections.map(renderSection)}

          {/* App Info */}
          <FoodWayCard variant="elevated" style={styles.appInfoCard}>
            <View style={styles.appInfo}>
              <View style={styles.appIconContainer}>
                <Ionicons name="restaurant" size={32} color={Colors.primary} />
              </View>
              <View style={styles.appDetails}>
                <FoodWayText variant="h4">FoodWay</FoodWayText>
                <FoodWayText variant="bodySmall" color="textSecondary">
                  Version 1.0.0
                </FoodWayText>
                <FoodWayText variant="caption" color="textLight">
                  Made with ❤️ for food lovers
                </FoodWayText>
              </View>
            </View>
          </FoodWayCard>

          {/* Logout Button */}
          <FoodWayButton
            title="Sign Out"
            variant="outline"
            leftIcon={<Ionicons name="log-out" size={18} color={Colors.error} />}
            onPress={handleLogout}
            style={styles.logoutButton}
          />

          <View style={styles.bottomSpacing} />
        </Animated.View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },

  scrollView: {
    flex: 1,
  },

  content: {
    paddingTop: 100, // Account for sticky header
    paddingHorizontal: Spacing.lg,
  },

  sectionCard: {
    marginBottom: Spacing.md,
  },

  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.md,
    paddingBottom: Spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },

  sectionIcon: {
    width: 40,
    height: 40,
    borderRadius: BorderRadius.full,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: Spacing.md,
  },

  sectionTitle: {
    flex: 1,
  },

  sectionItems: {
    gap: Spacing.xs,
  },

  settingItem: {
    paddingVertical: Spacing.sm,
  },

  settingItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },

  settingItemIcon: {
    width: 36,
    height: 36,
    borderRadius: BorderRadius.full,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: Spacing.md,
  },

  settingItemInfo: {
    flex: 1,
  },

  settingItemTitle: {
    marginBottom: 2,
  },

  appInfoCard: {
    marginBottom: Spacing.lg,
  },

  appInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },

  appIconContainer: {
    width: 60,
    height: 60,
    borderRadius: BorderRadius.lg,
    backgroundColor: Colors.primaryAlpha,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: Spacing.md,
  },

  appDetails: {
    flex: 1,
  },

  logoutButton: {
    borderColor: Colors.error,
    marginBottom: Spacing.lg,
  },

  bottomSpacing: {
    height: Spacing['6xl'],
  },
});
