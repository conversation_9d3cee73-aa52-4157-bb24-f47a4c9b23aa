/**
 * FoodWay Sticky Header Component
 * Modern sticky header with navigation and actions
 */

import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Animated,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { DesignSystem } from '../../constants/DesignSystem';

const { Colors, Typography, Spacing, BorderRadius, Shadows } = DesignSystem;

interface FoodWayStickyHeaderProps {
  title?: string;
  subtitle?: string;
  onBackPress?: () => void;
  onFavoritePress?: () => void;
  onSharePress?: () => void;
  isFavorite?: boolean;
  opacity?: Animated.AnimatedValue;
  backgroundColor?: string;
}

export const FoodWayStickyHeader: React.FC<FoodWayStickyHeaderProps> = ({
  title,
  subtitle,
  onBackPress,
  onFavoritePress,
  onSharePress,
  isFavorite = false,
  opacity,
  backgroundColor = Colors.white,
}) => {
  const insets = useSafeAreaInsets();

  const animatedStyle = opacity ? {
    opacity,
    backgroundColor,
  } : { backgroundColor };

  return (
    <Animated.View style={[styles.container, { paddingTop: insets.top }, animatedStyle]}>
      <View style={styles.content}>
        {/* Left Section */}
        <TouchableOpacity 
          style={styles.actionButton} 
          onPress={onBackPress}
          hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
        >
          <Ionicons name="arrow-back" size={24} color={Colors.textPrimary} />
        </TouchableOpacity>
        
        {/* Center Section */}
        <View style={styles.titleContainer}>
          {title && (
            <Text style={styles.title} numberOfLines={1}>
              {title}
            </Text>
          )}
          {subtitle && (
            <Text style={styles.subtitle} numberOfLines={1}>
              {subtitle}
            </Text>
          )}
        </View>
        
        {/* Right Section */}
        <View style={styles.rightActions}>
          <TouchableOpacity 
            style={styles.actionButton} 
            onPress={onSharePress}
            hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
          >
            <Ionicons name="share-outline" size={22} color={Colors.textPrimary} />
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={styles.actionButton} 
            onPress={onFavoritePress}
            hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
          >
            <Ionicons 
              name={isFavorite ? "heart" : "heart-outline"} 
              size={22} 
              color={isFavorite ? Colors.error : Colors.textPrimary} 
            />
          </TouchableOpacity>
        </View>
      </View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 1000,
    ...Shadows.sm,
  },
  
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
    minHeight: 56,
  },
  
  actionButton: {
    width: 40,
    height: 40,
    borderRadius: BorderRadius.full,
    alignItems: 'center',
    justifyContent: 'center',
  },
  
  titleContainer: {
    flex: 1,
    alignItems: 'center',
    paddingHorizontal: Spacing.md,
  },
  
  title: {
    fontSize: Typography.fontSize.lg,
    fontFamily: Typography.fontFamily.semibold,
    fontWeight: Typography.fontWeight.semibold,
    color: Colors.textPrimary,
    textAlign: 'center',
  },
  
  subtitle: {
    fontSize: Typography.fontSize.sm,
    fontFamily: Typography.fontFamily.regular,
    color: Colors.textSecondary,
    textAlign: 'center',
    marginTop: 2,
  },
  
  rightActions: {
    flexDirection: 'row',
    gap: Spacing.xs,
  },
});

export default FoodWayStickyHeader;
