/**
 * FoodWay Text Component
 * Enhanced typography component with consistent styling and variants
 */

import React from 'react';
import {
  Text,
  StyleSheet,
  TextProps,
  TextStyle,
} from 'react-native';
import { DesignSystem } from '../../constants/DesignSystem';

const { Colors, Typography } = DesignSystem;

interface FoodWayTextProps extends TextProps {
  variant?: 'h1' | 'h2' | 'h3' | 'h4' | 'body' | 'bodySmall' | 'caption' | 'button';
  color?: keyof typeof Colors;
  weight?: keyof typeof Typography.fontWeight;
  align?: 'left' | 'center' | 'right';
  children: React.ReactNode;
}

export const FoodWayText: React.FC<FoodWayTextProps> = ({
  variant = 'body',
  color = 'textPrimary',
  weight,
  align = 'left',
  style,
  children,
  ...props
}) => {
  const variantStyle = styles[variant];
  const colorValue = Colors[color as keyof typeof Colors] || Colors.textPrimary;
  
  const textStyles: TextStyle[] = [
    styles.base,
    variantStyle,
    { color: colorValue },
    weight && { fontWeight: Typography.fontWeight[weight] },
    { textAlign: align },
    style,
  ];

  return (
    <Text style={textStyles} {...props}>
      {children}
    </Text>
  );
};

const styles = StyleSheet.create({
  base: {
    fontFamily: Typography.fontFamily.regular,
    color: Colors.textPrimary,
  },
  
  // Heading variants
  h1: {
    fontSize: Typography.fontSize['4xl'],
    fontFamily: Typography.fontFamily.bold,
    fontWeight: Typography.fontWeight.bold,
    lineHeight: Typography.fontSize['4xl'] * Typography.lineHeight.tight,
    marginBottom: 16,
  },
  
  h2: {
    fontSize: Typography.fontSize['3xl'],
    fontFamily: Typography.fontFamily.bold,
    fontWeight: Typography.fontWeight.bold,
    lineHeight: Typography.fontSize['3xl'] * Typography.lineHeight.tight,
    marginBottom: 12,
  },
  
  h3: {
    fontSize: Typography.fontSize['2xl'],
    fontFamily: Typography.fontFamily.semibold,
    fontWeight: Typography.fontWeight.semibold,
    lineHeight: Typography.fontSize['2xl'] * Typography.lineHeight.normal,
    marginBottom: 8,
  },
  
  h4: {
    fontSize: Typography.fontSize.xl,
    fontFamily: Typography.fontFamily.semibold,
    fontWeight: Typography.fontWeight.semibold,
    lineHeight: Typography.fontSize.xl * Typography.lineHeight.normal,
    marginBottom: 6,
  },
  
  // Body variants
  body: {
    fontSize: Typography.fontSize.base,
    fontFamily: Typography.fontFamily.regular,
    fontWeight: Typography.fontWeight.regular,
    lineHeight: Typography.fontSize.base * Typography.lineHeight.normal,
  },
  
  bodySmall: {
    fontSize: Typography.fontSize.sm,
    fontFamily: Typography.fontFamily.regular,
    fontWeight: Typography.fontWeight.regular,
    lineHeight: Typography.fontSize.sm * Typography.lineHeight.normal,
  },
  
  caption: {
    fontSize: Typography.fontSize.xs,
    fontFamily: Typography.fontFamily.regular,
    fontWeight: Typography.fontWeight.regular,
    lineHeight: Typography.fontSize.xs * Typography.lineHeight.normal,
    color: Colors.textSecondary,
  },
  
  button: {
    fontSize: Typography.fontSize.base,
    fontFamily: Typography.fontFamily.semibold,
    fontWeight: Typography.fontWeight.semibold,
    lineHeight: Typography.fontSize.base * Typography.lineHeight.tight,
  },
});

export default FoodWayText;
