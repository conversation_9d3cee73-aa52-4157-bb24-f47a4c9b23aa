/**
 * FoodWay Favorites Screen
 * Modern favorites screen with prominent icons and restaurant management
 */

import React, { useState, useRef } from 'react';
import {
  View,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Animated,
  FlatList,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { 
  FoodWayText,
  FoodWayCard,
  FoodWayButton,
  FoodWayBadge,
  FoodWaySearchBar,
  FoodWayRestaurantCard,
  FoodWayStickyHeader,
  Colors,
  DesignSystem 
} from '../components/ui';

const { Spacing, BorderRadius, Typography, Shadows } = DesignSystem;

// Mock favorite restaurants with prominent icons
const favoriteRestaurants = [
  {
    id: '1',
    name: "Mario's Pizza Palace",
    image: 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=300&h=200&fit=crop',
    cuisine: ['Italian', 'Pizza'],
    rating: 4.5,
    reviewCount: 324,
    deliveryTime: '25-35 min',
    deliveryFee: 2.99,
    minimumOrder: 15,
    isOpen: true,
    offer: '20% OFF',
    tags: ['Popular', 'Fast Delivery'],
    dateAdded: '2024-01-15',
    lastOrdered: '2024-01-20',
    orderCount: 5,
  },
  {
    id: '2',
    name: "Burger Junction",
    image: 'https://images.unsplash.com/photo-1568901346375-23c9450c58cd?w=300&h=200&fit=crop',
    cuisine: ['American', 'Burgers'],
    rating: 4.3,
    reviewCount: 189,
    deliveryTime: '20-30 min',
    deliveryFee: 1.99,
    minimumOrder: 12,
    isOpen: true,
    offer: null,
    tags: ['Trending'],
    dateAdded: '2024-01-10',
    lastOrdered: '2024-01-18',
    orderCount: 3,
  },
  {
    id: '3',
    name: "Sushi Express",
    image: 'https://images.unsplash.com/photo-1579584425555-c3ce17fd4351?w=300&h=200&fit=crop',
    cuisine: ['Japanese', 'Sushi'],
    rating: 4.7,
    reviewCount: 256,
    deliveryTime: '30-40 min',
    deliveryFee: 3.99,
    minimumOrder: 20,
    isOpen: false,
    offer: 'Free Delivery',
    tags: ['Premium'],
    dateAdded: '2024-01-05',
    lastOrdered: '2024-01-15',
    orderCount: 2,
  },
];

export default function FavoritesScreen() {
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState('recent');
  const fadeAnim = useRef(new Animated.Value(0)).current;

  React.useEffect(() => {
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 500,
      useNativeDriver: true,
    }).start();
  }, []);

  const filteredFavorites = favoriteRestaurants
    .filter(restaurant => 
      restaurant.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      restaurant.cuisine.some(c => c.toLowerCase().includes(searchQuery.toLowerCase()))
    )
    .sort((a, b) => {
      switch (sortBy) {
        case 'recent':
          return new Date(b.lastOrdered).getTime() - new Date(a.lastOrdered).getTime();
        case 'rating':
          return b.rating - a.rating;
        case 'orders':
          return b.orderCount - a.orderCount;
        case 'name':
          return a.name.localeCompare(b.name);
        default:
          return 0;
      }
    });

  const handleRestaurantPress = (restaurant: any) => {
    router.push(`/restaurant/${restaurant.id}`);
  };

  const handleRemoveFavorite = (restaurantId: string) => {
    // Remove from favorites logic
    console.log('Remove favorite:', restaurantId);
  };

  const renderFavoriteCard = ({ item: restaurant }: { item: typeof favoriteRestaurants[0] }) => (
    <View style={styles.favoriteCardContainer}>
      <FoodWayRestaurantCard
        restaurant={restaurant}
        onPress={() => handleRestaurantPress(restaurant)}
        style={styles.restaurantCard}
      />
      
      {/* Favorite Actions */}
      <View style={styles.favoriteActions}>
        <View style={styles.favoriteStats}>
          <View style={styles.statItem}>
            <Ionicons name="receipt" size={16} color={Colors.primary} />
            <FoodWayText variant="caption" color="textSecondary" style={styles.statText}>
              {restaurant.orderCount} orders
            </FoodWayText>
          </View>
          
          <View style={styles.statItem}>
            <Ionicons name="time" size={16} color={Colors.textSecondary} />
            <FoodWayText variant="caption" color="textSecondary" style={styles.statText}>
              Last: {new Date(restaurant.lastOrdered).toLocaleDateString()}
            </FoodWayText>
          </View>
        </View>
        
        <TouchableOpacity
          style={styles.removeButton}
          onPress={() => handleRemoveFavorite(restaurant.id)}
          activeOpacity={0.8}
        >
          <Ionicons name="heart" size={20} color={Colors.error} />
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderEmptyState = () => (
    <FoodWayCard variant="elevated" style={styles.emptyState}>
      <View style={styles.emptyStateContent}>
        <Ionicons name="heart-outline" size={80} color={Colors.textLight} />
        <FoodWayText variant="h3" style={styles.emptyStateTitle}>
          No favorites yet
        </FoodWayText>
        <FoodWayText variant="body" color="textSecondary" style={styles.emptyStateSubtitle}>
          {searchQuery 
            ? 'No favorites match your search' 
            : 'Start adding restaurants to your favorites to see them here'
          }
        </FoodWayText>
        {!searchQuery && (
          <FoodWayButton
            title="Discover Restaurants"
            variant="primary"
            leftIcon={<Ionicons name="compass" size={18} color={Colors.white} />}
            onPress={() => router.push('/(tabs)/')}
            style={styles.discoverButton}
          />
        )}
      </View>
    </FoodWayCard>
  );

  const sortOptions = [
    { id: 'recent', label: 'Recently Ordered', icon: 'time' as const },
    { id: 'rating', label: 'Highest Rated', icon: 'star' as const },
    { id: 'orders', label: 'Most Ordered', icon: 'receipt' as const },
    { id: 'name', label: 'Name A-Z', icon: 'text' as const },
  ];

  return (
    <View style={styles.container}>
      <FoodWayStickyHeader
        title="Favorites"
        subtitle={`${favoriteRestaurants.length} saved restaurants`}
        onBackPress={() => router.back()}
      />

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <Animated.View style={[styles.content, { opacity: fadeAnim }]}>
          {/* Search Bar */}
          <View style={styles.searchContainer}>
            <FoodWaySearchBar
              placeholder="Search your favorites..."
              value={searchQuery}
              onChangeText={setSearchQuery}
              showFilter={false}
            />
          </View>

          {/* Sort Options */}
          <View style={styles.sortContainer}>
            <FoodWayText variant="h4" style={styles.sortTitle}>
              Sort by
            </FoodWayText>
            <ScrollView 
              horizontal 
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.sortOptions}
            >
              {sortOptions.map((option) => (
                <TouchableOpacity
                  key={option.id}
                  style={[
                    styles.sortOption,
                    sortBy === option.id && styles.sortOptionSelected,
                  ]}
                  onPress={() => setSortBy(option.id)}
                  activeOpacity={0.8}
                >
                  <Ionicons
                    name={option.icon}
                    size={16}
                    color={sortBy === option.id ? Colors.white : Colors.primary}
                  />
                  <FoodWayText
                    variant="bodySmall"
                    color={sortBy === option.id ? "white" : "primary"}
                    style={styles.sortOptionText}
                  >
                    {option.label}
                  </FoodWayText>
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>

          {/* Favorites List */}
          <View style={styles.favoritesContainer}>
            {filteredFavorites.length === 0 ? (
              renderEmptyState()
            ) : (
              <FlatList
                data={filteredFavorites}
                renderItem={renderFavoriteCard}
                keyExtractor={(item) => item.id}
                showsVerticalScrollIndicator={false}
                scrollEnabled={false}
                ItemSeparatorComponent={() => <View style={styles.favoriteSeparator} />}
              />
            )}
          </View>

          {/* Quick Actions */}
          {filteredFavorites.length > 0 && (
            <View style={styles.quickActions}>
              <FoodWayButton
                title="Clear All Favorites"
                variant="outline"
                leftIcon={<Ionicons name="trash" size={18} color={Colors.error} />}
                onPress={() => console.log('Clear all favorites')}
                style={styles.clearButton}
              />
            </View>
          )}

          <View style={styles.bottomSpacing} />
        </Animated.View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },

  scrollView: {
    flex: 1,
  },

  content: {
    paddingTop: 100, // Account for sticky header
  },

  searchContainer: {
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
  },

  sortContainer: {
    paddingHorizontal: Spacing.lg,
    marginBottom: Spacing.md,
  },

  sortTitle: {
    marginBottom: Spacing.sm,
  },

  sortOptions: {
    paddingRight: Spacing.lg,
  },

  sortOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    borderRadius: BorderRadius.full,
    backgroundColor: Colors.surface,
    marginRight: Spacing.sm,
    borderWidth: 1,
    borderColor: Colors.border,
  },

  sortOptionSelected: {
    backgroundColor: Colors.primary,
    borderColor: Colors.primary,
  },

  sortOptionText: {
    marginLeft: Spacing.xs,
  },

  favoritesContainer: {
    paddingHorizontal: Spacing.lg,
  },

  favoriteCardContainer: {
    marginBottom: Spacing.md,
  },

  restaurantCard: {
    marginBottom: Spacing.sm,
  },

  favoriteActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: Spacing.md,
  },

  favoriteStats: {
    flexDirection: 'row',
    gap: Spacing.lg,
  },

  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },

  statText: {
    marginLeft: Spacing.xs,
  },

  removeButton: {
    padding: Spacing.sm,
  },

  favoriteSeparator: {
    height: Spacing.md,
  },

  emptyState: {
    marginTop: Spacing['2xl'],
  },

  emptyStateContent: {
    alignItems: 'center',
    paddingVertical: Spacing['2xl'],
  },

  emptyStateTitle: {
    marginTop: Spacing.md,
    marginBottom: Spacing.sm,
    textAlign: 'center',
  },

  emptyStateSubtitle: {
    textAlign: 'center',
    marginBottom: Spacing.lg,
  },

  discoverButton: {
    marginTop: Spacing.md,
  },

  quickActions: {
    paddingHorizontal: Spacing.lg,
    marginTop: Spacing.lg,
  },

  clearButton: {
    borderColor: Colors.error,
  },

  bottomSpacing: {
    height: Spacing['6xl'],
  },
});
