/**
 * FoodWay Card Component
 * Modern card component with multiple variants and elevation levels
 */

import React from 'react';
import {
  View,
  StyleSheet,
  ViewStyle,
  ViewProps,
} from 'react-native';
import { DesignSystem } from '../../constants/DesignSystem';

const { Colors, Spacing, BorderRadius, ComponentVariants, Shadows } = DesignSystem;

interface FoodWayCardProps extends ViewProps {
  variant?: 'default' | 'elevated' | 'outlined';
  padding?: keyof typeof Spacing;
  children: React.ReactNode;
}

export const FoodWayCard: React.FC<FoodWayCardProps> = ({
  variant = 'default',
  padding = 'lg',
  children,
  style,
  ...props
}) => {
  const cardVariant = ComponentVariants.card[variant];
  
  const cardStyles: ViewStyle[] = [
    styles.base,
    cardVariant,
    { padding: Spacing[padding] },
    style,
  ];

  return (
    <View style={cardStyles} {...props}>
      {children}
    </View>
  );
};

const styles = StyleSheet.create({
  base: {
    borderRadius: BorderRadius.lg,
  },
});

export default FoodWayCard;
