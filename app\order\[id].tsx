/**
 * FoodWay Order Tracking Screen
 * Modern order tracking with live updates, prominent icons, and enhanced UX
 */

import React, { useEffect, useState } from 'react';
import {
  View,
  ScrollView,
  StyleSheet,
  RefreshControl,
  Animated,
} from 'react-native';
import { useLocalSearchParams, router } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { 
  FoodWayText,
  FoodWayStickyHeader,
  FoodWayCard,
  FoodWayButton,
  Colors,
  DesignSystem 
} from '../../components/ui';
import { FoodWayOrderProgress, OrderStep } from '../../components/ui/FoodWayOrderProgress';
import { FoodWayETADisplay } from '../../components/ui/FoodWayETADisplay';
import { FoodWayContactActions } from '../../components/ui/FoodWayContactActions';

const { Spacing, BorderRadius, Shadows } = DesignSystem;

// Mock order data
const mockOrder = {
  id: 'ORD-12345',
  restaurantName: "Mario's Pizza Palace",
  restaurantPhone: '******-0123',
  deliveryPersonName: '<PERSON>',
  deliveryPersonPhone: '******-0456',
  status: 'on_the_way' as const,
  estimatedTime: 25,
  items: [
    { name: 'Margherita Pizza', quantity: 1, price: 18.99 },
    { name: 'Caesar Salad', quantity: 1, price: 12.99 },
    { name: 'Garlic Bread', quantity: 2, price: 8.99 },
  ],
  total: 40.97,
  deliveryAddress: '123 Main St, Apt 4B, New York, NY 10001',
};

export default function OrderTrackingScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const [refreshing, setRefreshing] = useState(false);
  const [fadeAnim] = useState(new Animated.Value(0));

  // Mock order steps with prominent icons
  const orderSteps: OrderStep[] = [
    {
      id: '1',
      title: 'Order Placed',
      description: 'Your order has been confirmed',
      icon: 'receipt',
      status: 'completed',
      timestamp: '2:30 PM',
    },
    {
      id: '2',
      title: 'Preparing',
      description: 'Restaurant is preparing your food',
      icon: 'restaurant',
      status: 'completed',
      timestamp: '2:35 PM',
    },
    {
      id: '3',
      title: 'Ready for Pickup',
      description: 'Food is ready and waiting',
      icon: 'checkmark-circle',
      status: 'completed',
      timestamp: '2:50 PM',
    },
    {
      id: '4',
      title: 'Out for Delivery',
      description: 'Delivery partner is on the way',
      icon: 'bicycle',
      status: 'current',
      timestamp: '3:00 PM',
    },
    {
      id: '5',
      title: 'Delivered',
      description: 'Enjoy your meal!',
      icon: 'home',
      status: 'pending',
    },
  ];

  useEffect(() => {
    // Fade in animation
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 500,
      useNativeDriver: true,
    }).start();
  }, [fadeAnim]);

  const onRefresh = async () => {
    setRefreshing(true);
    // Simulate API call
    setTimeout(() => {
      setRefreshing(false);
    }, 1000);
  };

  const handleBackPress = () => {
    router.back();
  };

  const handleShareLocation = () => {
    // Implement location sharing
    console.log('Share location');
  };

  const renderOrderSummary = () => (
    <FoodWayCard variant="elevated" style={styles.orderSummary}>
      <View style={styles.orderHeader}>
        <View style={styles.orderIconContainer}>
          <Ionicons name="bag-check" size={24} color={Colors.primary} />
        </View>
        <View style={styles.orderInfo}>
          <FoodWayText variant="h4" style={styles.orderId}>
            Order #{mockOrder.id}
          </FoodWayText>
          <FoodWayText variant="body" color="textSecondary">
            {mockOrder.restaurantName}
          </FoodWayText>
        </View>
        <View style={styles.orderTotal}>
          <FoodWayText variant="h4" color="primary">
            ${mockOrder.total.toFixed(2)}
          </FoodWayText>
        </View>
      </View>

      <View style={styles.orderItems}>
        {mockOrder.items.map((item, index) => (
          <View key={index} style={styles.orderItem}>
            <FoodWayText variant="body" style={styles.itemName}>
              {item.quantity}x {item.name}
            </FoodWayText>
            <FoodWayText variant="body" color="textSecondary">
              ${item.price.toFixed(2)}
            </FoodWayText>
          </View>
        ))}
      </View>

      <View style={styles.deliveryAddress}>
        <Ionicons name="location" size={16} color={Colors.primary} />
        <FoodWayText variant="bodySmall" color="textSecondary" style={styles.addressText}>
          Delivering to: {mockOrder.deliveryAddress}
        </FoodWayText>
      </View>
    </FoodWayCard>
  );

  const renderQuickActions = () => (
    <View style={styles.quickActions}>
      <FoodWayButton
        title="Share Location"
        variant="outline"
        size="medium"
        leftIcon={<Ionicons name="share" size={18} color={Colors.primary} />}
        onPress={handleShareLocation}
        style={styles.actionButton}
      />
      
      <FoodWayButton
        title="Reorder"
        variant="ghost"
        size="medium"
        leftIcon={<Ionicons name="repeat" size={18} color={Colors.primary} />}
        onPress={() => console.log('Reorder')}
        style={styles.actionButton}
      />
    </View>
  );

  return (
    <View style={styles.container}>
      {/* Sticky Header */}
      <FoodWayStickyHeader
        title="Order Tracking"
        subtitle={`Order #${mockOrder.id}`}
        onBackPress={handleBackPress}
        onSharePress={() => console.log('Share order')}
      />

      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        <Animated.View style={[styles.content, { opacity: fadeAnim }]}>
          {/* ETA Display */}
          <FoodWayETADisplay
            estimatedTime={mockOrder.estimatedTime}
            status={mockOrder.status}
            deliveryPersonName={mockOrder.deliveryPersonName}
          />

          {/* Order Summary */}
          {renderOrderSummary()}

          {/* Order Progress */}
          <FoodWayOrderProgress
            steps={orderSteps}
            currentStep={3}
          />

          {/* Contact Actions */}
          <FoodWayContactActions
            restaurant={{
              name: mockOrder.restaurantName,
              phone: mockOrder.restaurantPhone,
              role: 'restaurant',
            }}
            deliveryPerson={{
              name: mockOrder.deliveryPersonName,
              phone: mockOrder.deliveryPersonPhone,
              role: 'delivery',
            }}
            orderId={mockOrder.id}
          />

          {/* Quick Actions */}
          {renderQuickActions()}

          {/* Bottom Spacing */}
          <View style={styles.bottomSpacing} />
        </Animated.View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },

  scrollView: {
    flex: 1,
  },

  content: {
    paddingTop: 100, // Account for sticky header
  },

  orderSummary: {
    marginHorizontal: Spacing.lg,
    marginBottom: Spacing.lg,
  },

  orderHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.md,
  },

  orderIconContainer: {
    width: 40,
    height: 40,
    borderRadius: BorderRadius.full,
    backgroundColor: Colors.primaryAlpha,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: Spacing.md,
  },

  orderInfo: {
    flex: 1,
  },

  orderId: {
    marginBottom: 2,
  },

  orderTotal: {
    alignItems: 'flex-end',
  },

  orderItems: {
    marginBottom: Spacing.md,
  },

  orderItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: Spacing.xs,
  },

  itemName: {
    flex: 1,
  },

  deliveryAddress: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    paddingTop: Spacing.md,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
  },

  addressText: {
    flex: 1,
    marginLeft: Spacing.sm,
    lineHeight: 18,
  },

  quickActions: {
    flexDirection: 'row',
    paddingHorizontal: Spacing.lg,
    gap: Spacing.md,
    marginBottom: Spacing.lg,
  },

  actionButton: {
    flex: 1,
  },

  bottomSpacing: {
    height: Spacing['6xl'],
  },
});
