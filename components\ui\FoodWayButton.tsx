/**
 * FoodWay Button Component
 * Modern, accessible button with multiple variants and states
 */

import React, { useRef } from 'react';
import {
    ActivityIndicator,
    Animated,
    Haptics,
    StyleSheet,
    Text,
    TextStyle,
    TouchableOpacity,
    TouchableOpacityProps,
    ViewStyle
} from 'react-native';
import { DesignSystem } from '../../constants/DesignSystem';

const { Colors, Typography, Spacing, BorderRadius, ComponentVariants, Animations } = DesignSystem;

interface FoodWayButtonProps extends TouchableOpacityProps {
  title: string;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger';
  size?: 'small' | 'medium' | 'large';
  loading?: boolean;
  disabled?: boolean;
  fullWidth?: boolean;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  iconName?: string; // For accessibility descriptions
  accessibilityHint?: string;
}

export const FoodWayButton: React.FC<FoodWayButtonProps> = ({
  title,
  variant = 'primary',
  size = 'medium',
  loading = false,
  disabled = false,
  fullWidth = false,
  leftIcon,
  rightIcon,
  iconName,
  accessibilityHint,
  style,
  onPress,
  ...props
}) => {
  // Animation setup
  const scaleValue = useRef(new Animated.Value(1)).current;

  // Performance optimizations - inline to avoid import issues
  const deviceSettings = {
    animationDuration: 300,
    enableHapticFeedback: true,
  };

  const buttonVariant = ComponentVariants.button[variant];

  const buttonStyles: ViewStyle[] = [
    styles.base,
    styles[size],
    buttonVariant,
    fullWidth && styles.fullWidth,
    disabled && styles.disabled,
    style,
  ];

  const textColor = variant === 'outline' || variant === 'ghost'
    ? Colors.primary
    : Colors.white;

  const textStyles: TextStyle[] = [
    styles.text,
    styles[`text${size.charAt(0).toUpperCase() + size.slice(1)}` as keyof typeof styles],
    { color: disabled ? Colors.textLight : textColor },
  ];

  // Animation handlers
  const handlePressIn = () => {
    if (!disabled && !loading) {
      Animated.timing(scaleValue, {
        toValue: 0.95,
        duration: deviceSettings.animationDuration / 3,
        useNativeDriver: true,
      }).start();
    }
  };

  const handlePressOut = () => {
    if (!disabled && !loading) {
      Animated.timing(scaleValue, {
        toValue: 1,
        duration: deviceSettings.animationDuration / 3,
        useNativeDriver: true,
      }).start();
    }
  };

  const handlePress = (event: any) => {
    if (!disabled && !loading) {
      // Add haptic feedback if enabled
      if (deviceSettings.enableHapticFeedback) {
        try {
          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
        } catch (error) {
          // Haptics not available, continue without feedback
        }
      }
      onPress?.(event);
    }
  };

  // Generate accessibility props
  const accessibilityProps = {
    accessible: true,
    accessibilityRole: 'button' as const,
    accessibilityLabel: iconName ? `${iconName}, ${title}` : title,
    accessibilityHint: accessibilityHint || (loading ? 'Loading' : undefined),
    accessibilityState: { disabled: disabled || loading },
  };

  return (
    <Animated.View style={{ transform: [{ scale: scaleValue }] }}>
      <TouchableOpacity
        style={buttonStyles}
        disabled={disabled || loading}
        activeOpacity={1}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        onPress={handlePress}
        {...accessibilityProps}
        {...props}
      >
        {loading ? (
          <ActivityIndicator
            color={textColor}
            size="small"
          />
        ) : (
          <>
            {leftIcon}
            <Text style={textStyles}>{title}</Text>
            {rightIcon}
          </>
        )}
      </TouchableOpacity>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  base: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: BorderRadius.md,
    paddingHorizontal: Spacing.lg,
    gap: Spacing.sm,
  },
  
  // Sizes
  small: {
    height: 36,
    paddingHorizontal: Spacing.md,
  },
  medium: {
    height: 48,
    paddingHorizontal: Spacing.lg,
  },
  large: {
    height: 56,
    paddingHorizontal: Spacing.xl,
  },
  
  // Text styles
  text: {
    fontFamily: Typography.fontFamily.semibold,
    fontWeight: Typography.fontWeight.semibold,
    textAlign: 'center',
  },
  textSmall: {
    fontSize: Typography.fontSize.sm,
  },
  textMedium: {
    fontSize: Typography.fontSize.base,
  },
  textLarge: {
    fontSize: Typography.fontSize.lg,
  },
  
  // States
  disabled: {
    opacity: 0.5,
  },
  fullWidth: {
    width: '100%',
  },
});

export default FoodWayButton;
