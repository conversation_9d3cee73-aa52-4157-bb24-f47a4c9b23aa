/**
 * FoodWay Text Styles
 * Pre-defined text styles for consistent typography throughout the app
 */

import { TextStyle } from 'react-native';
import { DesignSystem } from './DesignSystem';

const { Colors, Typography } = DesignSystem;

// === HEADING STYLES ===
export const headingStyles: Record<string, TextStyle> = {
  h1: {
    fontSize: Typography.fontSize['4xl'],
    fontFamily: Typography.fontFamily.bold,
    fontWeight: Typography.fontWeight.bold,
    lineHeight: Typography.fontSize['4xl'] * Typography.lineHeight.tight,
    color: Colors.textPrimary,
  },
  
  h2: {
    fontSize: Typography.fontSize['3xl'],
    fontFamily: Typography.fontFamily.bold,
    fontWeight: Typography.fontWeight.bold,
    lineHeight: Typography.fontSize['3xl'] * Typography.lineHeight.tight,
    color: Colors.textPrimary,
  },
  
  h3: {
    fontSize: Typography.fontSize['2xl'],
    fontFamily: Typography.fontFamily.semibold,
    fontWeight: Typography.fontWeight.semibold,
    lineHeight: Typography.fontSize['2xl'] * Typography.lineHeight.normal,
    color: Colors.textPrimary,
  },
  
  h4: {
    fontSize: Typography.fontSize.xl,
    fontFamily: Typography.fontFamily.semibold,
    fontWeight: Typography.fontWeight.semibold,
    lineHeight: Typography.fontSize.xl * Typography.lineHeight.normal,
    color: Colors.textPrimary,
  },
  
  h5: {
    fontSize: Typography.fontSize.lg,
    fontFamily: Typography.fontFamily.medium,
    fontWeight: Typography.fontWeight.medium,
    lineHeight: Typography.fontSize.lg * Typography.lineHeight.normal,
    color: Colors.textPrimary,
  },
};

// === BODY STYLES ===
export const bodyStyles: Record<string, TextStyle> = {
  large: {
    fontSize: Typography.fontSize.lg,
    fontFamily: Typography.fontFamily.regular,
    fontWeight: Typography.fontWeight.regular,
    lineHeight: Typography.fontSize.lg * Typography.lineHeight.normal,
    color: Colors.textPrimary,
  },
  
  regular: {
    fontSize: Typography.fontSize.base,
    fontFamily: Typography.fontFamily.regular,
    fontWeight: Typography.fontWeight.regular,
    lineHeight: Typography.fontSize.base * Typography.lineHeight.normal,
    color: Colors.textPrimary,
  },
  
  small: {
    fontSize: Typography.fontSize.sm,
    fontFamily: Typography.fontFamily.regular,
    fontWeight: Typography.fontWeight.regular,
    lineHeight: Typography.fontSize.sm * Typography.lineHeight.normal,
    color: Colors.textPrimary,
  },
  
  caption: {
    fontSize: Typography.fontSize.xs,
    fontFamily: Typography.fontFamily.regular,
    fontWeight: Typography.fontWeight.regular,
    lineHeight: Typography.fontSize.xs * Typography.lineHeight.normal,
    color: Colors.textSecondary,
  },
};

// === SPECIALIZED STYLES ===
export const specialStyles: Record<string, TextStyle> = {
  button: {
    fontSize: Typography.fontSize.base,
    fontFamily: Typography.fontFamily.semibold,
    fontWeight: Typography.fontWeight.semibold,
    lineHeight: Typography.fontSize.base * Typography.lineHeight.tight,
    color: Colors.white,
    textAlign: 'center',
  },
  
  link: {
    fontSize: Typography.fontSize.base,
    fontFamily: Typography.fontFamily.medium,
    fontWeight: Typography.fontWeight.medium,
    lineHeight: Typography.fontSize.base * Typography.lineHeight.normal,
    color: Colors.primary,
    textDecorationLine: 'underline',
  },
  
  price: {
    fontSize: Typography.fontSize.lg,
    fontFamily: Typography.fontFamily.bold,
    fontWeight: Typography.fontWeight.bold,
    lineHeight: Typography.fontSize.lg * Typography.lineHeight.tight,
    color: Colors.primary,
  },
  
  rating: {
    fontSize: Typography.fontSize.sm,
    fontFamily: Typography.fontFamily.semibold,
    fontWeight: Typography.fontWeight.semibold,
    lineHeight: Typography.fontSize.sm * Typography.lineHeight.tight,
    color: Colors.rating,
  },
  
  badge: {
    fontSize: Typography.fontSize.xs,
    fontFamily: Typography.fontFamily.semibold,
    fontWeight: Typography.fontWeight.semibold,
    lineHeight: Typography.fontSize.xs * Typography.lineHeight.tight,
    color: Colors.white,
    textAlign: 'center',
  },
  
  error: {
    fontSize: Typography.fontSize.sm,
    fontFamily: Typography.fontFamily.regular,
    fontWeight: Typography.fontWeight.regular,
    lineHeight: Typography.fontSize.sm * Typography.lineHeight.normal,
    color: Colors.error,
  },
  
  success: {
    fontSize: Typography.fontSize.sm,
    fontFamily: Typography.fontFamily.regular,
    fontWeight: Typography.fontWeight.regular,
    lineHeight: Typography.fontSize.sm * Typography.lineHeight.normal,
    color: Colors.success,
  },
};

// === COMBINED TEXT STYLES ===
export const textStyles = {
  ...headingStyles,
  ...bodyStyles,
  ...specialStyles,
};

// === UTILITY FUNCTIONS ===
export const getTextStyle = (variant: keyof typeof textStyles): TextStyle => {
  return textStyles[variant] || bodyStyles.regular;
};

export const combineTextStyles = (...styles: (TextStyle | undefined)[]): TextStyle => {
  return Object.assign({}, ...styles.filter(Boolean));
};

export default textStyles;
