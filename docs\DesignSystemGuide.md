# FoodWay Design System Documentation

## 🎨 Overview

The FoodWay Design System is a comprehensive UI/UX framework designed to create a premium food delivery experience that surpasses competitors like Foodpanda, Zomato, and DoorDash. Built with React Native and Expo, it emphasizes modern design, accessibility, and performance.

## 🌈 Color System

### Primary Brand Colors

```typescript
// Core Brand Colors
primary: '#E53E3E'        // FoodWay Red - CTAs, highlights, urgent actions
primaryDark: '#C53030'    // Darker variant for gradients and hover states
primaryLight: '#F56565'   // Lighter variant for backgrounds
white: '#FFFFFF'          // Pure white for backgrounds and cards
black: '#2D3748'          // Dark gray for primary text and headings
```

### Extended Color Palette

```typescript
// Text Colors
textPrimary: '#2D3748'    // Main text color
textSecondary: '#718096'  // Secondary text, descriptions
textLight: '#A0AEC0'      // Light text, captions

// Status Colors
success: '#38A169'        // Order confirmed, delivery status
warning: '#F6E05E'        // Discounts, limited offers
error: '#E53E3E'          // Error states, urgent notifications
info: '#3182CE'           // Links, informational elements

// Food-Specific Colors
rating: '#FFB400'         // Star ratings
veg: '#4CAF50'           // Vegetarian indicators
nonVeg: '#F44336'        // Non-vegetarian indicators
```

### Accessibility Compliance

All color combinations meet WCAG 2.1 AA standards:
- Primary text on white: 4.5:1 contrast ratio
- Secondary text on white: 4.5:1 contrast ratio
- Button text on primary: 4.5:1 contrast ratio

## 📝 Typography System

### Font Families

```typescript
fontFamily: {
  regular: Platform.OS === 'ios' ? 'SF Pro Text' : 'Inter-Regular',
  medium: Platform.OS === 'ios' ? 'SF Pro Text' : 'Inter-Medium',
  semibold: Platform.OS === 'ios' ? 'SF Pro Text' : 'Inter-SemiBold',
  bold: Platform.OS === 'ios' ? 'SF Pro Display' : 'Inter-Bold',
}
```

### Responsive Font Sizes

```typescript
fontSize: {
  xs: 12,    // Captions, small labels
  sm: 14,    // Secondary text
  base: 16,  // Body text
  lg: 18,    // Subheadings
  xl: 20,    // Card titles
  '2xl': 24, // Section headings
  '3xl': 30, // Page titles
  '4xl': 36, // Hero text
}
```

### Text Variants

- **h1-h4**: Heading variants with proper hierarchy
- **body**: Regular body text
- **bodySmall**: Smaller body text
- **caption**: Small captions and labels
- **button**: Button text styling

## 🧩 Component Library

### Core Components

#### FoodWayButton
Modern button with animations and accessibility features.

```tsx
<FoodWayButton
  title="Add to Cart"
  variant="primary"
  size="medium"
  leftIcon={<Ionicons name="add" size={18} />}
  iconName="add"
  accessibilityHint="Add item to your cart"
  onPress={handleAddToCart}
/>
```

**Variants**: `primary`, `secondary`, `outline`, `ghost`, `danger`
**Sizes**: `small`, `medium`, `large`

#### FoodWayCard
Flexible card component with elevation variants.

```tsx
<FoodWayCard variant="elevated" padding="lg">
  <FoodWayText variant="h3">Restaurant Name</FoodWayText>
  <FoodWayText variant="body">Description</FoodWayText>
</FoodWayCard>
```

**Variants**: `default`, `elevated`, `outlined`

#### FoodWayText
Typography component with semantic variants.

```tsx
<FoodWayText variant="h2" color="primary">
  Welcome to FoodWay
</FoodWayText>
```

#### FoodWayInput
Enhanced input with validation and icons.

```tsx
<FoodWayInput
  label="Search restaurants"
  leftIcon={<Ionicons name="search" size={20} />}
  placeholder="What are you craving?"
  onChangeText={setSearchQuery}
/>
```

### Specialized Components

#### FoodWayRestaurantCard
Restaurant display with ratings, delivery info, and offers.

#### FoodWayMenuItem
Menu item card with images, tags, and add buttons.

#### FoodWayOrderProgress
Order tracking with progress indicators and prominent icons.

#### FoodWayETADisplay
ETA countdown with animated status updates.

#### FoodWayFloatingCart
Floating cart button with item count and total.

## 🎭 Icons & Visual Elements

### Prominent Icon Usage

The design system emphasizes prominent, meaningful icons throughout:

```tsx
// Restaurant categories
<Ionicons name="restaurant" size={24} color={Colors.warning} />
<Ionicons name="basket" size={24} color={Colors.success} />
<Ionicons name="medical" size={24} color={Colors.info} />

// Order status
<Ionicons name="receipt" size={32} color={Colors.warning} />    // Order placed
<Ionicons name="flame" size={32} color={Colors.primary} />      // Cooking
<Ionicons name="bicycle" size={32} color={Colors.primary} />    // Delivery
<Ionicons name="home" size={32} color={Colors.success} />       // Delivered

// Actions
<Ionicons name="heart" size={22} color={Colors.error} />        // Favorite
<Ionicons name="share" size={22} color={Colors.primary} />      // Share
<Ionicons name="call" size={20} color={Colors.white} />         // Call
```

### Icon Guidelines

1. **Size Consistency**: Use 16px, 20px, 24px, or 32px sizes
2. **Color Meaning**: Match icon colors to their semantic meaning
3. **Accessibility**: Always provide icon descriptions for screen readers
4. **Animation**: Use subtle animations for interactive icons

## ✨ Animations & Micro-Interactions

### Animation Timing

```typescript
timing: {
  fast: 150,      // Button press feedback
  normal: 300,    // Standard transitions
  slow: 500,      // Complex animations
}
```

### Common Animations

1. **Button Press**: Scale to 0.95 with haptic feedback
2. **Card Hover**: Subtle scale to 1.02
3. **Loading States**: Smooth skeleton animations
4. **Page Transitions**: Slide with fade effects
5. **Status Updates**: Pulse animations for active states

### Performance Optimizations

- Use `useNativeDriver: true` for all animations
- Implement device-specific animation durations
- Disable complex animations on low-end devices
- Optimize FlatList rendering with proper configurations

## ♿ Accessibility Features

### WCAG 2.1 AA Compliance

1. **Color Contrast**: All text meets 4.5:1 minimum ratio
2. **Touch Targets**: Minimum 44x44 point touch areas
3. **Screen Reader**: Comprehensive accessibility labels
4. **Keyboard Navigation**: Full keyboard support
5. **Focus Management**: Proper focus indicators

### Accessibility Props

```tsx
// Automatic accessibility props generation
const accessibilityProps = createButtonAccessibilityProps(
  'Add to Cart',
  'Add this item to your shopping cart',
  false,
  'add',
  'plus icon'
);
```

### Screen Reader Support

- Semantic HTML roles
- Descriptive labels for all interactive elements
- Status announcements for dynamic content
- Proper heading hierarchy

## 📱 Performance Optimizations

### Image Optimization

```typescript
// Responsive image sizing
const optimizedImage = getOptimalImageSize(
  originalWidth,
  originalHeight,
  maxWidth,
  maxHeight
);
```

### List Performance

```typescript
// Optimized FlatList configuration
const listConfig = getFlatListOptimization(itemHeight);
```

### Device-Specific Settings

```typescript
// Adjust performance based on device capabilities
const settings = getDeviceOptimizedSettings();
```

## 🚀 Usage Examples

### Restaurant Card Implementation

```tsx
<FoodWayRestaurantCard
  restaurant={{
    id: '1',
    name: "Mario's Pizza Palace",
    image: 'https://example.com/image.jpg',
    rating: 4.5,
    deliveryTime: '25-30 min',
    deliveryFee: '$2.99',
    cuisine: ['Italian', 'Pizza'],
    offer: '20% OFF',
  }}
  onPress={(restaurant) => navigateToRestaurant(restaurant.id)}
/>
```

### Order Tracking Implementation

```tsx
<FoodWayOrderProgress
  steps={orderSteps}
  currentStep={2}
/>

<FoodWayETADisplay
  estimatedTime={25}
  status="on_the_way"
  deliveryPersonName="Alex Johnson"
/>
```

## 🎯 Best Practices

### Component Usage

1. **Consistency**: Always use design system components
2. **Accessibility**: Include proper accessibility props
3. **Performance**: Use optimized configurations for lists
4. **Icons**: Provide meaningful icons with descriptions
5. **Animations**: Use appropriate timing and easing

### Development Guidelines

1. **Import Structure**: Use centralized component exports
2. **Styling**: Leverage design system constants
3. **Testing**: Test with screen readers and keyboard navigation
4. **Performance**: Monitor render counts and animation performance

## 📚 Resources

- **Component Storybook**: Interactive component documentation
- **Accessibility Testing**: VoiceOver (iOS) and TalkBack (Android)
- **Performance Monitoring**: React DevTools Profiler
- **Design Tokens**: Figma design system file

## 🛠️ Quick Start Guide

### Installation & Setup

1. **Import Components**:
```tsx
import {
  FoodWayButton,
  FoodWayCard,
  FoodWayText,
  Colors,
  DesignSystem
} from '../components/ui';
```

2. **Use Design Tokens**:
```tsx
const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.background,
    padding: DesignSystem.Spacing.lg,
  },
});
```

3. **Implement Accessibility**:
```tsx
import { createButtonAccessibilityProps } from '../utils/accessibility';

const accessibilityProps = createButtonAccessibilityProps(
  'Order Now',
  'Place your food order',
  false,
  'restaurant'
);
```

### Common Patterns

#### Screen Layout
```tsx
<SafeAreaView style={styles.container}>
  <FoodWayStickyHeader title="Restaurant" onBackPress={goBack} />
  <ScrollView>
    <FoodWayCard variant="elevated">
      <FoodWayText variant="h2">Content</FoodWayText>
    </FoodWayCard>
  </ScrollView>
  <FoodWayFloatingCart itemCount={3} totalAmount={45.99} />
</SafeAreaView>
```

#### Form Implementation
```tsx
<FoodWayInput
  label="Email"
  leftIcon={<Ionicons name="mail" size={20} />}
  value={email}
  onChangeText={setEmail}
  error={emailError}
/>
<FoodWayButton
  title="Sign In"
  variant="primary"
  fullWidth
  loading={isLoading}
  onPress={handleSignIn}
/>
```

---

*This design system is continuously evolving. For updates and contributions, please refer to the project repository.*
