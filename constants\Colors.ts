/**
 * FoodWay App Color System
 * Premium food delivery app colors designed to surpass competitors
 * Optimized for user experience, visual appeal, and brand recognition
 */

// FoodWay Brand Colors
const primaryRed = '#E53E3E';
const primaryRedDark = '#C53030';
const primaryRedLight = '#F56565';
const pureWhite = '#FFFFFF';
const darkGray = '#2D3748';

export const Colors = {
  // === CORE BRAND COLORS ===
  primary: primaryRed,
  primaryDark: primaryRedDark,
  primaryLight: primaryRedLight,
  primaryAlpha: 'rgba(229, 62, 62, 0.1)',

  white: pureWhite,
  black: darkGray,

  // === EXTENDED COLOR PALETTE ===
  // Text Colors
  textPrimary: darkGray,
  textSecondary: '#718096',
  textLight: '#A0AEC0',
  textWhite: pureWhite,

  // Background Colors
  background: pureWhite,
  backgroundSecondary: '#F7FAFC',
  surface: '#F8F9FA',
  surfaceElevated: pureWhite,

  // Status Colors
  success: '#38A169',
  successLight: '#68D391',
  successAlpha: 'rgba(56, 161, 105, 0.1)',

  warning: '#F6E05E',
  warningDark: '#D69E2E',
  warningAlpha: 'rgba(246, 224, 94, 0.1)',

  error: '#E53E3E',
  errorLight: '#F56565',
  errorAlpha: 'rgba(229, 62, 62, 0.1)',

  info: '#3182CE',
  infoLight: '#63B3ED',
  infoAlpha: 'rgba(49, 130, 206, 0.1)',

  // UI Colors
  border: '#E2E8F0',
  borderLight: '#F1F5F9',
  borderDark: '#CBD5E0',

  // Food-Specific Colors
  rating: '#FFB400',
  discount: '#E91E63',
  veg: '#4CAF50',
  nonVeg: '#F44336',

  // Gradient Colors
  gradientStart: primaryRed,
  gradientEnd: primaryRedDark,

  // === THEME VARIANTS ===
  light: {
    text: darkGray,
    background: pureWhite,
    tint: primaryRed,
    icon: '#718096',
    tabIconDefault: '#A0AEC0',
    tabIconSelected: primaryRed,
    border: '#E2E8F0',
    card: pureWhite,
    notification: primaryRed,
  },
  dark: {
    text: '#F7FAFC',
    background: '#1A202C',
    tint: primaryRedLight,
    icon: '#A0AEC0',
    tabIconDefault: '#718096',
    tabIconSelected: primaryRedLight,
    border: '#4A5568',
    card: '#2D3748',
    notification: primaryRedLight,
  },
};
