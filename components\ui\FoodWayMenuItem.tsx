/**
 * FoodWay Menu Item Component
 * Modern menu item card with image, details, tags, and add button
 */

import React from 'react';
import {
  View,
  Text,
  Image,
  TouchableOpacity,
  StyleSheet,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { DesignSystem } from '../../constants/DesignSystem';
import { FoodWayCard } from './FoodWayCard';
import { FoodWayBadge } from './FoodWayBadge';

const { Colors, Typography, Spacing, BorderRadius, Shadows } = DesignSystem;

interface MenuItem {
  id: string;
  name: string;
  description: string;
  image: string;
  price: number;
  isVegetarian?: boolean;
  isVegan?: boolean;
  isSpicy?: boolean;
  preparationTime?: string;
  calories?: number;
  isAvailable?: boolean;
}

interface FoodWayMenuItemProps {
  item: MenuItem;
  onPress?: (item: MenuItem) => void;
  onAddPress?: (item: MenuItem) => void;
}

export const FoodWayMenuItem: React.FC<FoodWayMenuItemProps> = ({
  item,
  onPress,
  onAddPress,
}) => {
  const handlePress = () => {
    onPress?.(item);
  };

  const handleAddPress = (e: any) => {
    e.stopPropagation();
    onAddPress?.(item);
  };

  return (
    <TouchableOpacity onPress={handlePress} activeOpacity={0.8}>
      <FoodWayCard variant="default" padding="md" style={styles.container}>
        <View style={styles.content}>
          <View style={styles.imageContainer}>
            <Image source={{ uri: item.image }} style={styles.image} />
            {!item.isAvailable && (
              <View style={styles.unavailableOverlay}>
                <Text style={styles.unavailableText}>Unavailable</Text>
              </View>
            )}
          </View>
          
          <View style={styles.details}>
            <Text style={styles.name} numberOfLines={2}>
              {item.name}
            </Text>
            
            <Text style={styles.description} numberOfLines={2}>
              {item.description}
            </Text>
            
            {/* Tags */}
            <View style={styles.tags}>
              {item.isVegetarian && (
                <FoodWayBadge 
                  text="VEG" 
                  variant="veg" 
                  size="small"
                  icon={<Ionicons name="leaf" size={10} color={Colors.white} />}
                />
              )}
              {item.isVegan && (
                <FoodWayBadge 
                  text="VEGAN" 
                  variant="success" 
                  size="small"
                />
              )}
              {item.isSpicy && (
                <FoodWayBadge 
                  text="SPICY" 
                  variant="error" 
                  size="small"
                  icon={<Ionicons name="flame" size={10} color={Colors.white} />}
                />
              )}
            </View>
            
            {/* Footer */}
            <View style={styles.footer}>
              <View style={styles.priceContainer}>
                <Text style={styles.price}>${item.price.toFixed(2)}</Text>
                {item.preparationTime && (
                  <View style={styles.timeContainer}>
                    <Ionicons name="time-outline" size={12} color={Colors.textSecondary} />
                    <Text style={styles.time}>{item.preparationTime}</Text>
                  </View>
                )}
              </View>
              
              <TouchableOpacity 
                style={[
                  styles.addButton,
                  !item.isAvailable && styles.addButtonDisabled
                ]} 
                onPress={handleAddPress}
                disabled={!item.isAvailable}
              >
                <Ionicons 
                  name="add" 
                  size={18} 
                  color={item.isAvailable ? Colors.white : Colors.textLight} 
                />
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </FoodWayCard>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: Spacing.md,
  },
  
  content: {
    flexDirection: 'row',
  },
  
  imageContainer: {
    position: 'relative',
    marginRight: Spacing.md,
  },
  
  image: {
    width: 80,
    height: 80,
    borderRadius: BorderRadius.md,
  },
  
  unavailableOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    borderRadius: BorderRadius.md,
    alignItems: 'center',
    justifyContent: 'center',
  },
  
  unavailableText: {
    fontSize: Typography.fontSize.xs,
    fontFamily: Typography.fontFamily.semibold,
    fontWeight: Typography.fontWeight.semibold,
    color: Colors.white,
  },
  
  details: {
    flex: 1,
  },
  
  name: {
    fontSize: Typography.fontSize.base,
    fontFamily: Typography.fontFamily.semibold,
    fontWeight: Typography.fontWeight.semibold,
    color: Colors.textPrimary,
    marginBottom: Spacing.xs,
  },
  
  description: {
    fontSize: Typography.fontSize.sm,
    fontFamily: Typography.fontFamily.regular,
    color: Colors.textSecondary,
    lineHeight: Typography.fontSize.sm * Typography.lineHeight.normal,
    marginBottom: Spacing.sm,
  },
  
  tags: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: Spacing.xs,
    marginBottom: Spacing.sm,
  },
  
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  
  priceContainer: {
    flex: 1,
  },
  
  price: {
    fontSize: Typography.fontSize.lg,
    fontFamily: Typography.fontFamily.bold,
    fontWeight: Typography.fontWeight.bold,
    color: Colors.primary,
    marginBottom: 2,
  },
  
  timeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 2,
  },
  
  time: {
    fontSize: Typography.fontSize.xs,
    fontFamily: Typography.fontFamily.regular,
    color: Colors.textSecondary,
  },
  
  addButton: {
    width: 32,
    height: 32,
    borderRadius: BorderRadius.full,
    backgroundColor: Colors.primary,
    alignItems: 'center',
    justifyContent: 'center',
    ...Shadows.sm,
  },
  
  addButtonDisabled: {
    backgroundColor: Colors.border,
  },
});

export default FoodWayMenuItem;
