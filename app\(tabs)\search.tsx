import { Ionicons } from '@expo/vector-icons';
import { router, useLocalSearchParams } from 'expo-router';
import React, { useEffect, useRef, useState } from 'react';
import {
  Animated,
  FlatList,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  View
} from 'react-native';
import {
  Colors,
  DesignSystem,
  FoodWayBadge,
  FoodWayCard,
  FoodWayRestaurantCard,
  FoodWaySearchBar,
  FoodWayText
} from '../../components/ui';
import { Category, Restaurant } from '../../types';

const { Spacing, BorderRadius, Typography, Shadows } = DesignSystem;

// Using centralized mock data service
// Temporary fallback data while imports are being resolved
const fallbackCategories: Category[] = [
  { id: '1', name: 'Pizza', image: 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=100&h=100&fit=crop' },
  { id: '2', name: 'Burgers', image: 'https://images.unsplash.com/photo-1568901346375-23c9450c58cd?w=100&h=100&fit=crop' },
  { id: '3', name: '<PERSON><PERSON>', image: 'https://images.unsplash.com/photo-1579584425555-c3ce17fd4351?w=100&h=100&fit=crop' },
  { id: '4', name: 'Mexican', image: 'https://images.unsplash.com/photo-1565299507177-b0ac66763828?w=100&h=100&fit=crop' },
];

// Enhanced categories with prominent icons
const enhancedCategories = [
  { id: '1', name: 'Pizza', icon: 'pizza' as const },
  { id: '2', name: 'Burgers', icon: 'fast-food' as const },
  { id: '3', name: 'Sushi', icon: 'fish' as const },
  { id: '4', name: 'Mexican', icon: 'restaurant' as const },
  { id: '5', name: 'Chinese', icon: 'restaurant-outline' as const },
  { id: '6', name: 'Indian', icon: 'flame' as const },
  { id: '7', name: 'Desserts', icon: 'ice-cream' as const },
  { id: '8', name: 'Coffee', icon: 'cafe' as const },
];

const fallbackRestaurants: Restaurant[] = [
  {
    id: '1',
    name: 'Mario\'s Pizza Palace',
    description: 'Authentic Italian pizza made with fresh ingredients',
    image: 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=300&h=200&fit=crop',
    cuisine: ['Italian', 'Pizza'],
    rating: 4.5,
    reviewCount: 324,
    deliveryTime: '25-35 min',
    deliveryFee: 2.99,
    minimumOrder: 15,
    isOpen: true,
    address: '123 Main St, Downtown',
    latitude: 37.7749,
    longitude: -122.4194,
    phone: '******-0123',
    categories: [],
    featured: true,
    promoted: false,
    tags: ['Popular', 'Fast Delivery'],
  },
  {
    id: '2',
    name: 'Burger Junction',
    description: 'Gourmet burgers and crispy fries',
    image: 'https://images.unsplash.com/photo-1568901346375-23c9450c58cd?w=300&h=200&fit=crop',
    cuisine: ['American', 'Burgers'],
    rating: 4.3,
    reviewCount: 189,
    deliveryTime: '20-30 min',
    deliveryFee: 1.99,
    minimumOrder: 12,
    isOpen: true,
    address: '456 Oak Ave, Midtown',
    latitude: 37.7849,
    longitude: -122.4094,
    phone: '******-0124',
    categories: [],
    featured: false,
    promoted: true,
    tags: ['New', 'Highly Rated'],
  },
];



const filterOptions = [
  { id: 'rating', label: 'Rating 4.0+', icon: 'star' as const, color: Colors.warning },
  { id: 'delivery', label: 'Free Delivery', icon: 'bicycle' as const, color: Colors.success },
  { id: 'fast', label: 'Fast Delivery', icon: 'flash' as const, color: Colors.primary },
  { id: 'new', label: 'New', icon: 'sparkles' as const, color: Colors.info },
  { id: 'offers', label: 'Offers', icon: 'pricetag' as const, color: Colors.error },
  { id: 'veg', label: 'Vegetarian', icon: 'leaf' as const, color: Colors.success },
];

export default function SearchScreen() {
  const params = useLocalSearchParams();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string | null>(
    params.category as string || null
  );
  const [selectedFilters, setSelectedFilters] = useState<string[]>([]);
  const [restaurants, setRestaurants] = useState<Restaurant[]>([]);
  const [allRestaurants, setAllRestaurants] = useState<Restaurant[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);

  // Modern animations
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(30)).current;
  const searchBarAnim = useRef(new Animated.Value(0)).current;

  // Load data from mock API
  const loadData = async () => {
    try {
      setLoading(true);

      try {
        // Import mock API service dynamically
        const { mockApiService } = await import('../../services/mock/mockApiService');

        // Load categories
        const categoriesResponse = await mockApiService.restaurants.getCategories();
        if (categoriesResponse.success) {
          setCategories(categoriesResponse.data || []);
        }

        // Load restaurants
        const restaurantsResponse = await mockApiService.restaurants.getRestaurants();
        if (restaurantsResponse.success) {
          const allRestaurantsData = restaurantsResponse.data || [];
          setAllRestaurants(allRestaurantsData);
          setRestaurants(allRestaurantsData); // Initially show all restaurants
        }
      } catch (importError) {
        console.warn('Failed to load from mock API service, using fallback data:', importError);
        // Use fallback data if import fails
        setCategories(fallbackCategories);
        setAllRestaurants(fallbackRestaurants);
        setRestaurants(fallbackRestaurants);
      }
    } catch (error) {
      console.error('Error loading data:', error);
      // Use fallback data as last resort
      setCategories(fallbackCategories);
      setAllRestaurants(fallbackRestaurants);
      setRestaurants(fallbackRestaurants);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // Initialize animations
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 600,
        useNativeDriver: true,
      }),
      Animated.timing(searchBarAnim, {
        toValue: 1,
        duration: 400,
        useNativeDriver: true,
      }),
    ]).start();

    // Load initial data
    loadData();
  }, []);

  // Filter restaurants based on search query, category, and filters
  useEffect(() => {
    let filtered = allRestaurants;

    if (searchQuery) {
      filtered = filtered.filter(restaurant =>
        restaurant.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        restaurant.cuisine.some(c => c.toLowerCase().includes(searchQuery.toLowerCase()))
      );
    }

    if (selectedCategory) {
      filtered = filtered.filter(restaurant =>
        restaurant.cuisine.includes(selectedCategory)
      );
    }

    setRestaurants(filtered);
  }, [searchQuery, selectedCategory, selectedFilters, allRestaurants]);

  const handleCategoryPress = (category: Category) => {
    setSelectedCategory(selectedCategory === category.name ? null : category.name);
  };

  const handleFilterPress = (filterId: string) => {
    setSelectedFilters(prev =>
      prev.includes(filterId)
        ? prev.filter(id => id !== filterId)
        : [...prev, filterId]
    );
  };

  const handleRestaurantPress = (restaurant: Restaurant) => {
    router.push(`/restaurant/${restaurant.id}`);
  };

  return (
    <View style={styles.container}>
      {/* Modern Search Header */}
      <Animated.View style={[styles.searchHeader, {
        opacity: searchBarAnim,
        transform: [{ translateY: slideAnim }]
      }]}>
        <FoodWaySearchBar
          placeholder="Search restaurants, cuisines..."
          value={searchQuery}
          onChangeText={setSearchQuery}
          onFilterPress={() => console.log('Filter pressed')}
          showFilter={true}
        />
      </Animated.View>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <Animated.View style={[styles.content, { opacity: fadeAnim }]}>
          {/* Categories Section */}
          <FoodWayCard variant="default" style={styles.categoriesSection}>
            <FoodWayText variant="h3" style={styles.sectionTitle}>
              Browse Categories
            </FoodWayText>
            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.categoriesList}
            >
              {enhancedCategories.map((category) => (
                <TouchableOpacity
                  key={category.id}
                  style={[
                    styles.categoryChip,
                    selectedCategory === category.name && styles.categoryChipSelected,
                  ]}
                  onPress={() => handleCategoryPress(category)}
                  activeOpacity={0.8}
                >
                  <View style={styles.categoryIconContainer}>
                    <Ionicons
                      name={category.icon}
                      size={20}
                      color={selectedCategory === category.name ? Colors.white : Colors.primary}
                    />
                  </View>
                  <FoodWayText
                    variant="bodySmall"
                    color={selectedCategory === category.name ? "white" : "textPrimary"}
                    style={styles.categoryText}
                  >
                    {category.name}
                  </FoodWayText>
                </TouchableOpacity>
              ))}
            </ScrollView>
          </FoodWayCard>

          {/* Filters Section */}
          <FoodWayCard variant="default" style={styles.filtersSection}>
            <FoodWayText variant="h3" style={styles.sectionTitle}>
              Quick Filters
            </FoodWayText>
            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.filtersList}
            >
              {filterOptions.map((filter) => (
                <TouchableOpacity
                  key={filter.id}
                  style={[
                    styles.filterChip,
                    selectedFilters.includes(filter.id) && styles.filterChipSelected,
                  ]}
                  onPress={() => handleFilterPress(filter.id)}
                  activeOpacity={0.8}
                >
                  <Ionicons
                    name={filter.icon}
                    size={16}
                    color={selectedFilters.includes(filter.id) ? Colors.white : filter.color}
                  />
                  <FoodWayText
                    variant="bodySmall"
                    color={selectedFilters.includes(filter.id) ? "white" : "textPrimary"}
                    style={styles.filterText}
                  >
                    {filter.label}
                  </FoodWayText>
                </TouchableOpacity>
              ))}
            </ScrollView>
          </FoodWayCard>

          {/* Results Section */}
          <View style={styles.resultsSection}>
            <View style={styles.resultsHeader}>
              <FoodWayText variant="h3" style={styles.resultsTitle}>
                {restaurants.length} restaurants found
              </FoodWayText>
              {searchQuery && (
                <FoodWayBadge
                  text={`"${searchQuery}"`}
                  variant="info"
                  style={styles.searchBadge}
                />
              )}
            </View>

            {restaurants.length === 0 ? (
              <FoodWayCard variant="elevated" style={styles.emptyResults}>
                <View style={styles.emptyResultsContent}>
                  <Ionicons name="search" size={60} color={Colors.textLight} />
                  <FoodWayText variant="h4" style={styles.emptyResultsTitle}>
                    No restaurants found
                  </FoodWayText>
                  <FoodWayText variant="body" color="textSecondary" style={styles.emptyResultsSubtitle}>
                    Try adjusting your search or filters
                  </FoodWayText>
                </View>
              </FoodWayCard>
            ) : (
              <FlatList
                data={restaurants}
                renderItem={({ item }) => (
                  <FoodWayRestaurantCard
                    restaurant={item}
                    onPress={() => handleRestaurantPress(item)}
                    style={styles.restaurantCard}
                  />
                )}
                keyExtractor={(item) => item.id}
                showsVerticalScrollIndicator={false}
                scrollEnabled={false}
                ItemSeparatorComponent={() => <View style={styles.restaurantSeparator} />}
              />
            )}
          </View>
        </Animated.View>
      </ScrollView>
    </View>
  );
}

// Modern styles for search screen
const modernStyles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  searchHeader: {
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    backgroundColor: COLORS.white,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 3,
  },
  categoriesSection: {
    backgroundColor: COLORS.white,
    paddingVertical: SPACING.md,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  sectionTitle: {
    fontSize: TYPOGRAPHY.fontSize.lg,
    fontWeight: '700',
    color: COLORS.textPrimary,
    marginBottom: SPACING.sm,
    paddingHorizontal: SPACING.md,
  },
  categoryItem: {
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    marginHorizontal: SPACING.xs,
    borderRadius: BORDER_RADIUS.full,
    backgroundColor: '#F3F4F6',
    borderWidth: 1,
    borderColor: 'transparent',
  },
  categoryItemSelected: {
    backgroundColor: COLORS.primary,
    borderColor: COLORS.primary,
  },
  categoryText: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    fontWeight: '600',
    color: COLORS.textSecondary,
  },
  categoryTextSelected: {
    color: COLORS.white,
  },
  filtersSection: {
    backgroundColor: COLORS.white,
    paddingVertical: SPACING.sm,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  filterItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: SPACING.sm,
    paddingVertical: SPACING.xs,
    marginHorizontal: SPACING.xs,
    borderRadius: BORDER_RADIUS.md,
    backgroundColor: '#F9FAFB',
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  filterItemSelected: {
    backgroundColor: '#FEF3E2',
    borderColor: COLORS.primary,
  },
  filterIcon: {
    marginRight: SPACING.xs,
  },
  filterText: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    fontWeight: '500',
    color: COLORS.textSecondary,
  },
  filterTextSelected: {
    color: COLORS.primary,
  },
  restaurantsList: {
    flex: 1,
    paddingTop: SPACING.sm,
  },
  restaurantCard: {
    marginHorizontal: SPACING.md,
    marginBottom: SPACING.md,
    borderRadius: BORDER_RADIUS.lg,
    backgroundColor: COLORS.white,
    overflow: 'hidden',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  restaurantImage: {
    width: '100%',
    height: 160,
    backgroundColor: '#F3F4F6',
  },
  restaurantInfo: {
    padding: SPACING.md,
  },
  restaurantName: {
    fontSize: TYPOGRAPHY.fontSize.lg,
    fontWeight: '700',
    color: COLORS.textPrimary,
    marginBottom: SPACING.xs,
  },
  restaurantDescription: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    color: COLORS.textSecondary,
    marginBottom: SPACING.sm,
    lineHeight: 20,
  },
  restaurantMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  metaLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: SPACING.md,
  },
  ratingText: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    fontWeight: '600',
    color: COLORS.textPrimary,
    marginLeft: 4,
  },
  deliveryTime: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    color: COLORS.textSecondary,
  },
  deliveryFee: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    fontWeight: '600',
    color: COLORS.primary,
  },
});

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },

  searchHeader: {
    backgroundColor: Colors.white,
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
    ...Shadows.sm,
  },

  scrollView: {
    flex: 1,
  },

  content: {
    paddingBottom: Spacing['6xl'],
  },
  categoriesSection: {
    marginHorizontal: Spacing.lg,
    marginBottom: Spacing.md,
  },

  filtersSection: {
    marginHorizontal: Spacing.lg,
    marginBottom: Spacing.md,
  },

  sectionTitle: {
    marginBottom: Spacing.md,
  },
  categoriesList: {
    paddingHorizontal: Spacing.sm,
  },

  categoryChip: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.surface,
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    borderRadius: BorderRadius.full,
    marginRight: Spacing.sm,
    borderWidth: 1,
    borderColor: Colors.border,
    minWidth: 100,
  },

  categoryChipSelected: {
    backgroundColor: Colors.primary,
    borderColor: Colors.primary,
  },

  categoryIconContainer: {
    marginRight: Spacing.xs,
  },

  categoryText: {
    fontSize: Typography.fontSize.sm,
    fontWeight: Typography.fontWeight.medium,
  },
  filtersList: {
    paddingHorizontal: Spacing.sm,
  },

  filterChip: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.surface,
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    borderRadius: BorderRadius.full,
    marginRight: Spacing.sm,
    borderWidth: 1,
    borderColor: Colors.border,
  },

  filterChipSelected: {
    backgroundColor: Colors.primary,
    borderColor: Colors.primary,
  },

  filterText: {
    marginLeft: Spacing.xs,
    fontSize: Typography.fontSize.sm,
    fontWeight: Typography.fontWeight.medium,
  },
  resultsSection: {
    marginHorizontal: Spacing.lg,
    marginBottom: Spacing.md,
  },

  resultsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.md,
  },

  resultsTitle: {
    flex: 1,
  },

  searchBadge: {
    marginLeft: Spacing.sm,
  },

  emptyResults: {
    marginTop: Spacing.xl,
  },

  emptyResultsContent: {
    alignItems: 'center',
    paddingVertical: Spacing.xl,
  },

  emptyResultsTitle: {
    marginTop: Spacing.md,
    marginBottom: Spacing.sm,
    textAlign: 'center',
  },

  emptyResultsSubtitle: {
    textAlign: 'center',
  },

  restaurantCard: {
    marginBottom: Spacing.md,
  },

  restaurantSeparator: {
    height: Spacing.sm,
  },
});
