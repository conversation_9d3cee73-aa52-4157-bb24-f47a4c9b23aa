import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { router } from 'expo-router';
import React, { useEffect, useRef } from 'react';
import {
    Alert,
    Animated,
    Image,
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import {
    DesignSystem
} from '../../components/ui';
import { useAuthStore } from '../../store/authStore';

const { Spacing, BorderRadius, Typography, Shadows } = DesignSystem;

// Enhanced profile menu items with prominent icons and colors
const profileMenuItems = [
  {
    id: 'orders',
    title: 'Order History',
    subtitle: 'View your past orders',
    icon: 'receipt' as const,
    color: Colors.primary,
    route: '/profile/orders',
  },
  {
    id: 'personal-info',
    title: 'Personal Information',
    subtitle: 'Update your details',
    icon: 'person' as const,
    color: Colors.info,
    route: '/profile/personal-info',
  },
  {
    id: 'addresses',
    title: 'Delivery Addresses',
    subtitle: 'Manage your addresses',
    icon: 'location' as const,
    color: Colors.success,
    route: '/profile/addresses',
  },
  {
    id: 'payment-methods',
    title: 'Payment Methods',
    subtitle: 'Manage cards and payments',
    icon: 'card' as const,
    color: Colors.warning,
    route: '/profile/payment-methods',
  },
  {
    id: 'favorites',
    title: 'Favorite Restaurants',
    subtitle: 'Your saved restaurants',
    icon: 'heart' as const,
    color: Colors.error,
    route: '/profile/favorites',
  },
  {
    id: 'notifications',
    title: 'Notifications',
    subtitle: 'Manage your preferences',
    icon: 'notifications' as const,
    color: Colors.info,
    route: '/profile/notifications',
  },
  {
    id: 'help',
    title: 'Help & Support',
    subtitle: 'Get help and contact us',
    icon: 'help-circle' as const,
    color: Colors.primary,
    route: '/profile/help',
  },
  {
    id: 'about',
    title: 'About FoodWay',
    subtitle: 'App version and info',
    icon: 'information-circle' as const,
    color: Colors.textSecondary,
    route: '/profile/about',
  },
];

export default function ProfileScreen() {
  const { user, logout, isAuthenticated } = useAuthStore();

  // Modern animations
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(30)).current;
  const profileAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    // Initialize animations
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 600,
        useNativeDriver: true,
      }),
      Animated.timing(profileAnim, {
        toValue: 1,
        duration: 400,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  const handleLogout = () => {
    Alert.alert(
      'Sign Out',
      'Are you sure you want to sign out?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Sign Out',
          style: 'destructive',
          onPress: async () => {
            await logout();
            router.replace('/(auth)/login');
          },
        },
      ]
    );
  };

  const handleMenuItemPress = (route: string) => {
    router.push(route as any);
  };

  if (!isAuthenticated || !user) {
    return (
      <SafeAreaView style={modernStyles.container}>
        <Animated.View style={[modernStyles.authPrompt, { opacity: fadeAnim }]}>
          <View style={modernStyles.authIcon}>
            <Ionicons name="person-circle-outline" size={40} color={COLORS.textSecondary} />
          </View>
          <Text style={modernStyles.authTitle}>Sign In Required</Text>
          <Text style={modernStyles.authSubtitle}>
            Please sign in to access your profile and manage your account settings
          </Text>
          <TouchableOpacity
            style={modernStyles.authButton}
            onPress={() => router.push('/(auth)/login')}
            activeOpacity={0.8}
          >
            <Text style={modernStyles.authButtonText}>Sign In</Text>
          </TouchableOpacity>
        </Animated.View>
      </SafeAreaView>
    );
  }

  return (
    <View style={styles.container}>
      {/* Modern Header with Gradient */}
      <Animated.View style={[styles.header, {
        opacity: profileAnim,
        transform: [{ translateY: slideAnim }]
      }]}>
        <LinearGradient
          colors={[Colors.primary, Colors.primaryDark]}
          style={styles.headerGradient}
        >
          <FoodWayText variant="h2" color="white" style={styles.headerTitle}>
            Profile
          </FoodWayText>
        </LinearGradient>
      </Animated.View>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Modern Profile Header */}
        <Animated.View style={{ opacity: fadeAnim }}>
          <FoodWayCard variant="elevated" style={styles.profileCard}>
            <View style={styles.profileInfo}>
              <View style={styles.avatarContainer}>
                {user.avatar ? (
                  <Image source={{ uri: user.avatar }} style={styles.avatar} />
                ) : (
                  <View style={styles.avatarPlaceholder}>
                    <Ionicons name="person" size={32} color={Colors.textSecondary} />
                  </View>
                )}
                <TouchableOpacity style={styles.editAvatarButton} activeOpacity={0.8}>
                  <Ionicons name="camera" size={16} color={Colors.white} />
                </TouchableOpacity>
              </View>

              <FoodWayText variant="h3" style={styles.userName}>
                {user.firstName} {user.lastName}
              </FoodWayText>
              <FoodWayText variant="body" color="textSecondary" style={styles.userEmail}>
                {user.email}
              </FoodWayText>
              {user.phone && (
                <FoodWayText variant="bodySmall" color="textSecondary" style={styles.userPhone}>
                  {user.phone}
                </FoodWayText>
              )}
              <FoodWayBadge
                text={user.isVerified ? 'Verified Account' : 'Verify Account'}
                variant={user.isVerified ? 'success' : 'warning'}
                icon={user.isVerified ? 'checkmark-circle' : 'alert-circle'}
                style={styles.verificationBadge}
              />
            </View>
          </FoodWayCard>
        </Animated.View>

        {/* Quick Stats */}
        <FoodWayCard variant="elevated" style={styles.statsCard}>
          <View style={styles.statsContainer}>
            <View style={styles.statItem}>
              <View style={styles.statIconContainer}>
                <Ionicons name="receipt" size={20} color={Colors.primary} />
              </View>
              <FoodWayText variant="h3" color="primary" style={styles.statValue}>
                12
              </FoodWayText>
              <FoodWayText variant="bodySmall" color="textSecondary">
                Orders
              </FoodWayText>
            </View>

            <View style={styles.statDivider} />

            <View style={styles.statItem}>
              <View style={styles.statIconContainer}>
                <Ionicons name="heart" size={20} color={Colors.error} />
              </View>
              <FoodWayText variant="h3" color="primary" style={styles.statValue}>
                5
              </FoodWayText>
              <FoodWayText variant="bodySmall" color="textSecondary">
                Favorites
              </FoodWayText>
            </View>

            <View style={styles.statDivider} />

            <View style={styles.statItem}>
              <View style={styles.statIconContainer}>
                <Ionicons name="star" size={20} color={Colors.warning} />
              </View>
              <FoodWayText variant="h3" color="primary" style={styles.statValue}>
                4.8
              </FoodWayText>
              <FoodWayText variant="bodySmall" color="textSecondary">
                Rating
              </FoodWayText>
            </View>
          </View>
        </FoodWayCard>

        {/* Modern Menu Items */}
        <Animated.View style={[styles.menuSection, {
          opacity: fadeAnim,
          transform: [{ translateY: slideAnim }]
        }]}>
          {profileMenuItems.map((item, index) => (
            <FoodWayCard
              key={item.id}
              variant="default"
              style={styles.menuItem}
            >
              <TouchableOpacity
                onPress={() => handleMenuItemPress(item.route)}
                activeOpacity={0.8}
                style={styles.menuItemContent}
              >
                <View style={[styles.menuIcon, { backgroundColor: `${item.color}20` }]}>
                  <Ionicons name={item.icon} size={22} color={item.color} />
                </View>
                <View style={styles.menuItemInfo}>
                  <FoodWayText variant="h4" style={styles.menuItemTitle}>
                    {item.title}
                  </FoodWayText>
                  <FoodWayText variant="bodySmall" color="textSecondary">
                    {item.subtitle}
                  </FoodWayText>
                </View>
                <Ionicons name="chevron-forward" size={20} color={Colors.textSecondary} />
              </TouchableOpacity>
            </FoodWayCard>
          ))}
        </Animated.View>

        {/* Modern Logout Button */}
        <Animated.View style={{ opacity: fadeAnim }}>
          <FoodWayButton
            title="Sign Out"
            variant="outline"
            leftIcon={<Ionicons name="log-out" size={18} color={Colors.error} />}
            onPress={handleLogout}
            style={styles.logoutButton}
          />
        </Animated.View>

        {/* App Version */}
        <FoodWayText variant="caption" color="textLight" style={styles.appVersion}>
          FoodWay v1.0.0 • Made with ❤️ for food lovers
        </FoodWayText>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },

  header: {
    backgroundColor: Colors.white,
    ...Shadows.sm,
  },

  headerGradient: {
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.lg,
    paddingTop: Spacing.xl,
  },

  headerTitle: {
    textAlign: 'center',
  },
  scrollView: {
    flex: 1,
  },
  profileCard: {
    margin: SPACING.md,
    marginTop: -SPACING.lg,
    borderRadius: BORDER_RADIUS.xl,
    backgroundColor: COLORS.white,
    padding: SPACING.lg,
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
  },
  profileInfo: {
    alignItems: 'center',
    marginBottom: SPACING.lg,
  },
  avatarContainer: {
    position: 'relative',
    marginBottom: SPACING.md,
  },
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
    borderWidth: 3,
    borderColor: COLORS.white,
  },
  avatarPlaceholder: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#F3F4F6',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 3,
    borderColor: COLORS.white,
  },
  editAvatarButton: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: COLORS.primary,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    borderColor: COLORS.white,
  },
  userName: {
    fontSize: TYPOGRAPHY.fontSize.xl,
    fontWeight: '700',
    color: COLORS.textPrimary,
    marginBottom: 4,
  },
  userEmail: {
    fontSize: TYPOGRAPHY.fontSize.md,
    color: COLORS.textSecondary,
    marginBottom: 4,
  },
  userPhone: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    color: COLORS.textSecondary,
    marginBottom: SPACING.sm,
  },
  verificationBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F0FDF4',
    paddingHorizontal: SPACING.sm,
    paddingVertical: SPACING.xs,
    borderRadius: BORDER_RADIUS.full,
  },
  verificationText: {
    fontSize: TYPOGRAPHY.fontSize.xs,
    fontWeight: '600',
    marginLeft: 4,
  },
  menuSection: {
    margin: SPACING.md,
  },
  menuItem: {
    backgroundColor: COLORS.white,
    borderRadius: BORDER_RADIUS.lg,
    marginBottom: SPACING.sm,
    overflow: 'hidden',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
  },
  menuItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: SPACING.md,
  },
  menuIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F3F4F6',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: SPACING.md,
  },
  menuItemInfo: {
    flex: 1,
  },
  menuItemTitle: {
    fontSize: TYPOGRAPHY.fontSize.md,
    fontWeight: '600',
    color: COLORS.textPrimary,
    marginBottom: 2,
  },
  menuItemSubtitle: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    color: COLORS.textSecondary,
  },
  logoutButton: {
    margin: SPACING.md,
    marginTop: SPACING.lg,
    backgroundColor: '#FEF2F2',
    borderRadius: BORDER_RADIUS.lg,
    overflow: 'hidden',
  },
  logoutContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: SPACING.md,
  },
  logoutText: {
    fontSize: TYPOGRAPHY.fontSize.md,
    fontWeight: '600',
    color: '#DC2626',
    marginLeft: SPACING.sm,
  },
  authPrompt: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: SPACING.xl,
  },
  authIcon: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#F3F4F6',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: SPACING.lg,
  },
  authTitle: {
    fontSize: TYPOGRAPHY.fontSize.xl,
    fontWeight: '700',
    color: COLORS.textPrimary,
    marginBottom: SPACING.sm,
    textAlign: 'center',
  },
  authSubtitle: {
    fontSize: TYPOGRAPHY.fontSize.md,
    color: COLORS.textSecondary,
    textAlign: 'center',
    marginBottom: SPACING.xl,
    lineHeight: 22,
  },
  authButton: {
    backgroundColor: COLORS.primary,
    paddingHorizontal: SPACING.xl,
    paddingVertical: SPACING.md,
    borderRadius: BORDER_RADIUS.lg,
  },
  authButtonText: {
    fontSize: TYPOGRAPHY.fontSize.md,
    fontWeight: '600',
    color: COLORS.white,
  },
});
