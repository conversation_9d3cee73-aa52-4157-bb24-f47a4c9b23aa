/**
 * FoodWay Floating Cart Component
 * Modern floating cart button with item count and total
 */

import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Animated,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { DesignSystem } from '../../constants/DesignSystem';

const { Colors, Typography, Spacing, BorderRadius, Shadows } = DesignSystem;

interface FoodWayFloatingCartProps {
  itemCount: number;
  totalAmount: number;
  onPress?: () => void;
  visible?: boolean;
}

export const FoodWayFloatingCart: React.FC<FoodWayFloatingCartProps> = ({
  itemCount,
  totalAmount,
  onPress,
  visible = true,
}) => {
  const insets = useSafeAreaInsets();

  if (!visible || itemCount === 0) {
    return null;
  }

  return (
    <View style={[styles.container, { bottom: insets.bottom + Spacing.lg }]}>
      <TouchableOpacity 
        style={styles.button} 
        onPress={onPress}
        activeOpacity={0.8}
      >
        <View style={styles.content}>
          {/* Left Section - Cart Icon and Count */}
          <View style={styles.leftSection}>
            <View style={styles.iconContainer}>
              <Ionicons name="bag" size={20} color={Colors.white} />
              {itemCount > 0 && (
                <View style={styles.badge}>
                  <Text style={styles.badgeText}>
                    {itemCount > 99 ? '99+' : itemCount.toString()}
                  </Text>
                </View>
              )}
            </View>
            <Text style={styles.itemText}>
              {itemCount} {itemCount === 1 ? 'item' : 'items'}
            </Text>
          </View>
          
          {/* Right Section - Total and Arrow */}
          <View style={styles.rightSection}>
            <Text style={styles.totalText}>
              ${totalAmount.toFixed(2)}
            </Text>
            <Ionicons name="chevron-forward" size={16} color={Colors.white} />
          </View>
        </View>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    left: Spacing.lg,
    right: Spacing.lg,
    zIndex: 1000,
  },
  
  button: {
    backgroundColor: Colors.primary,
    borderRadius: BorderRadius.lg,
    ...Shadows.lg,
  },
  
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
    minHeight: 56,
  },
  
  leftSection: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  
  iconContainer: {
    position: 'relative',
    marginRight: Spacing.sm,
  },
  
  badge: {
    position: 'absolute',
    top: -6,
    right: -6,
    backgroundColor: Colors.white,
    borderRadius: BorderRadius.full,
    minWidth: 18,
    height: 18,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 4,
  },
  
  badgeText: {
    fontSize: Typography.fontSize.xs,
    fontFamily: Typography.fontFamily.bold,
    fontWeight: Typography.fontWeight.bold,
    color: Colors.primary,
    lineHeight: 14,
  },
  
  itemText: {
    fontSize: Typography.fontSize.base,
    fontFamily: Typography.fontFamily.medium,
    fontWeight: Typography.fontWeight.medium,
    color: Colors.white,
  },
  
  rightSection: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.sm,
  },
  
  totalText: {
    fontSize: Typography.fontSize.lg,
    fontFamily: Typography.fontFamily.bold,
    fontWeight: Typography.fontWeight.bold,
    color: Colors.white,
  },
});

export default FoodWayFloatingCart;
